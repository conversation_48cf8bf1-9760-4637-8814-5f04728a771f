{"uuid": "723f37ae-5355-499f-b3a9-643edd3c7a54", "historyId": "b9d17034bdc181e1e138d48e1e07844f", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518970943, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber POST", "stop": 1755518970943}], "attachments": [], "parameters": [], "start": 1755518970807, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 29 more characters, failOnStatusCode: false, body: { isdCode: '55', telephoneNumber: '11111111111' } }\")", "stop": 1755518970943}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100294\",\n  \"message\": \"Esse número de celular já está cadastrado.\"\n}"}], "start": 1755518970947, "name": "assert expected **{ Object (code, message) }** to have property **code**", "stop": 1755518970947}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "100294"}, {"name": "expected", "value": "100294"}], "start": 1755518970947, "name": "assert expected **{ Object (code, message) }** to have property **code** of **'100294'**", "stop": 1755518970947}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100294\",\n  \"message\": \"Esse número de celular já está cadastrado.\"\n}"}], "start": 1755518970947, "name": "assert expected **{ Object (code, message) }** to have property **message**", "stop": 1755518970947}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "Esse número de celular já está cadastrado."}, {"name": "expected", "value": "Esse número de celular já está cadastrado."}], "start": 1755518970947, "name": "assert expected **{ Object (code, message) }** to have property **message** of **Esse número de celular já está cadastrado.**", "stop": 1755518970947}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 400}], "start": 1755518970947, "name": "assert expected **400** to equal **400**", "stop": 1755518970947}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518970780, "name": "Deve retornar 400 quando o número de telefone já está cadastrado", "fullName": "Deve retornar 400 quando o número de telefone já está cadastrado", "stop": 1755518970950}
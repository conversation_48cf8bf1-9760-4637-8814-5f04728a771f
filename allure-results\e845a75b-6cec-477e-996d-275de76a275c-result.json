{"uuid": "e845a75b-6cec-477e-996d-275de76a275c", "historyId": "6026084c87bdcfdc8b7f6f434d2e81ee", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518969933, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf POST", "stop": 1755518969933}], "attachments": [], "parameters": [], "start": 1755518969642, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 33 more characters, failOnStatusCode: false, body: { cpf: '80807788090' } }\")", "stop": 1755518969933}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"INVALID_DOCUMENT\",\n  \"message\": \"O documento solicitado não está em um formato válido.\"\n}"}], "start": 1755518969940, "name": "assert expected **{ Object (code, message) }** to have property **code**", "stop": 1755518969940}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "INVALID_DOCUMENT"}, {"name": "expected", "value": "INVALID_DOCUMENT"}], "start": 1755518969941, "name": "assert expected **{ Object (code, message) }** to have property **code** of **INVALID_DOCUMENT**", "stop": 1755518969941}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"INVALID_DOCUMENT\",\n  \"message\": \"O documento solicitado não está em um formato válido.\"\n}"}], "start": 1755518969941, "name": "assert expected **{ Object (code, message) }** to have property **message**", "stop": 1755518969941}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "O documento solicitado não está em um formato válido."}, {"name": "expected", "value": "O documento solicitado não está em um formato válido."}], "start": 1755518969941, "name": "assert expected **{ Object (code, message) }** to have property **message** of **O documento solicitado não está em um formato válido.**", "stop": 1755518969941}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 422}, {"name": "expected", "value": 422}], "start": 1755518969941, "name": "assert expected **422** to equal **422**", "stop": 1755518969941}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518969561, "name": "Deve retornar 422 e mensagem de documento inválido", "fullName": "Deve retornar 422 e mensagem de documento inválido", "stop": 1755518969944}
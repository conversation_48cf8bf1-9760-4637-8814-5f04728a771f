{"stats": {"suites": 3, "tests": 6, "passes": 6, "pending": 0, "failures": 0, "start": "2025-08-13T23:50:06.213Z", "end": "2025-08-13T23:53:28.823Z", "duration": 202610, "testsRegistered": 6, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "f3faa907-2192-41c9-9eba-53375fc31907", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "file": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "5f1ee8d4-e5b6-41e3-b956-40be4f6097dc", "title": "<PERSON>e validar o login", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Validando elementos da tela", "fullTitle": "Deve validar o login Validando elementos da tela", "timedOut": null, "duration": 33066, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Suporte\ncy.validateText('span.d_block', 'Suporte');\n//Valida logo do cliente\ncy.get(_locators.default.LOGIN.LOGO_CLIENTE).should('be.visible').and('have.attr', 'src').and('match', /\\/logo-estrelabet\\.svg$/);\n//Validação do título e subtítulo\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.LOGIN_PAGE.TITLE);\n//Validando campo email, título e placeholder\ncy.validateText(_locators.default.LOGIN.EMAIL_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.EMAIL_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL);\n//Validando campo senha, título e placeholder\ncy.validateText(_locators.default.LOGIN.SENHA_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.SENHA_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA);\n// Digitar senha\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type('123456').should('have.attr', 'type', 'password'); // Verifica que está oculta\n// Clicar no botão de mostrar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).should('be.visible').and('exist').click();\n// Verificar que a senha está visível\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'text');\n// Clicar novamente para ocultar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).click();\n// Verificar que a senha voltou a ser oculta\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'password');\n//Botão esqueci a senha\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "b6c06618-790e-46d0-a47f-7a4bdd3dbe5a", "parentUUID": "5f1ee8d4-e5b6-41e3-b956-40be4f6097dc", "isHook": false, "skipped": false}, {"title": "Deve realizar o login com sucesso", "fullTitle": "Deve validar o login Deve realizar o login com sucesso", "timedOut": null, "duration": 42989, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "const username = Cypress.env('user_name');\nconst password = Cypress.env('user_password');\nexpect(username, 'Nome de usuário').to.be.a('string').and.not.be.empty;\nif (typeof password !== 'string' || !password) {\n  throw new Error('O valor senha está ausente, inclua a senha usando o cy.env');\n}\ncy.get(_locators.default.LOGIN.EMAIL_CAMPO).type(username).should('have.value', username);\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(password, {\n  log: false\n}).should(el$ => {\n  if (el$.val() !== password) {\n    throw new Error('Valor diferente da senha digitada');\n  }\n});\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 200);\ncy.get('@login').then(({\n  request,\n  response\n}) => {\n  expect(request.method).to.equal('POST');\n  cy.isVisible('.nebulosa-avatar__fallback:eq(2)');\n});", "err": {}, "uuid": "4932d79a-518f-46a1-884b-c536d26e8ed9", "parentUUID": "5f1ee8d4-e5b6-41e3-b956-40be4f6097dc", "isHook": false, "skipped": false}], "suites": [{"uuid": "59fe90d1-aeb4-4fb2-a960-375d64a0589d", "title": "Deve validar padrões de email incorreto", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve validar login credenciais incorretas", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar login credenciais incorretas", "timedOut": null, "duration": 18515, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, '<EMAIL>');\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '123456Uds@');\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 401);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO);", "err": {}, "uuid": "cf2cc0f2-d0c6-45a3-96c0-ea7a7d5c80ab", "parentUUID": "59fe90d1-aeb4-4fb2-a960-375d64a0589d", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres no email", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres no email", "timedOut": null, "duration": 5865, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, 'qa');\ncy.get(_locators.default.MENSAGENS_ERRO.EMAIL_INVÁLIDO).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "25ffcfac-c034-434a-945e-e17d4804de3a", "parentUUID": "59fe90d1-aeb4-4fb2-a960-375d64a0589d", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres na senha", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres na senha", "timedOut": null, "duration": 4221, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '1234567');\ncy.get(_locators.default.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "013d2dde-3e8e-4dde-a8d4-f16cbb02eb52", "parentUUID": "59fe90d1-aeb4-4fb2-a960-375d64a0589d", "isHook": false, "skipped": false}], "suites": [], "passes": ["cf2cc0f2-d0c6-45a3-96c0-ea7a7d5c80ab", "25ffcfac-c034-434a-945e-e17d4804de3a", "013d2dde-3e8e-4dde-a8d4-f16cbb02eb52"], "failures": [], "pending": [], "skipped": [], "duration": 28601, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": ["b6c06618-790e-46d0-a47f-7a4bdd3dbe5a", "4932d79a-518f-46a1-884b-c536d26e8ed9"], "failures": [], "pending": [], "skipped": [], "duration": 76055, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "69ca19ce-b55b-4fcb-9822-632d24b54d39", "title": "<PERSON>e validar o logout", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve realizar o logout", "fullTitle": "Deve validar o logout Deve realizar o logout", "timedOut": null, "duration": 26994, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Validando avatar\ncy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Abrindo opções do usuário\ncy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Clicando em sair\ncy.contains('section.d_flex > div', 'Sair').should('be.visible').click();\ncy.wait('@logout').its('response.statusCode').should('eq', 200);", "err": {}, "uuid": "44c039a0-0ef1-453a-ba43-3d4195ef824b", "parentUUID": "69ca19ce-b55b-4fcb-9822-632d24b54d39", "isHook": false, "skipped": false}], "suites": [], "passes": ["44c039a0-0ef1-453a-ba43-3d4195ef824b"], "failures": [], "pending": [], "skipped": [], "duration": 26994, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
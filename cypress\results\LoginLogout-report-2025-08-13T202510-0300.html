<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:3,&quot;tests&quot;:6,&quot;passes&quot;:6,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-08-13T23:23:26.340Z&quot;,&quot;end&quot;:&quot;2025-08-13T23:25:10.825Z&quot;,&quot;duration&quot;:104485,&quot;testsRegistered&quot;:6,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;57d29c6a-f8e6-4d5b-91b2-3afcc22487ab&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\LoginLogout.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;80b07b92-2070-471d-b59a-64e7575f67e1&quot;,&quot;title&quot;:&quot;Deve validar o login&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Validando elementos da tela&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Validando elementos da tela&quot;,&quot;duration&quot;:7697,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;medium&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;//Suporte\ncy.validateText(&#x27;span.d_block&#x27;, &#x27;Suporte&#x27;);\n//Valida logo do cliente\ncy.get(_locators.default.LOGIN.LOGO_CLIENTE).should(&#x27;be.visible&#x27;).and(&#x27;have.attr&#x27;, &#x27;src&#x27;).and(&#x27;match&#x27;, /\\/logo-estrelabet\\.svg$/);\n//Validação do título e subtítulo\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.LOGIN_PAGE.TITLE);\n//Validando campo email, título e placeholder\ncy.validateText(_locators.default.LOGIN.EMAIL_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.EMAIL_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL);\n//Validando campo senha, título e placeholder\ncy.validateText(_locators.default.LOGIN.SENHA_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.SENHA_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA);\n// Digitar senha\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(&#x27;123456&#x27;).should(&#x27;have.attr&#x27;, &#x27;type&#x27;, &#x27;password&#x27;); // Verifica que está oculta\n// Clicar no botão de mostrar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).should(&#x27;be.visible&#x27;).and(&#x27;exist&#x27;).click();\n// Verificar que a senha está visível\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should(&#x27;have.attr&#x27;, &#x27;type&#x27;, &#x27;text&#x27;);\n// Clicar novamente para ocultar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).click();\n// Verificar que a senha voltou a ser oculta\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should(&#x27;have.attr&#x27;, &#x27;type&#x27;, &#x27;password&#x27;);\n//Botão esqueci a senha\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should(&#x27;have.text&#x27;, &#x27;Entrar&#x27;).and(&#x27;be.visible&#x27;).and(&#x27;be.disabled&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;5629ccb7-ce8c-46b7-aaad-4c8744a06069&quot;,&quot;parentUUID&quot;:&quot;80b07b92-2070-471d-b59a-64e7575f67e1&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve realizar o login com sucesso&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve realizar o login com sucesso&quot;,&quot;duration&quot;:13766,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;const username = Cypress.env(&#x27;user_name&#x27;);\nconst password = Cypress.env(&#x27;user_password&#x27;);\nexpect(username, &#x27;Nome de usuário&#x27;).to.be.a(&#x27;string&#x27;).and.not.be.empty;\nif (typeof password !== &#x27;string&#x27; || !password) {\n  throw new Error(&#x27;O valor senha está ausente, inclua a senha usando o cy.env&#x27;);\n}\ncy.get(_locators.default.LOGIN.EMAIL_CAMPO).type(username).should(&#x27;have.value&#x27;, username);\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(password, {\n  log: false\n}).should(el$ =&gt; {\n  if (el$.val() !== password) {\n    throw new Error(&#x27;Valor diferente da senha digitada&#x27;);\n  }\n});\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait(&#x27;@login&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 200);\ncy.get(&#x27;@login&#x27;).then(({\n  request,\n  response\n}) =&gt; {\n  expect(request.method).to.equal(&#x27;POST&#x27;);\n  cy.isVisible(&#x27;.nebulosa-avatar__fallback:eq(2)&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;276aca10-8f6a-4c36-b427-da9fa71c35b9&quot;,&quot;parentUUID&quot;:&quot;80b07b92-2070-471d-b59a-64e7575f67e1&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;f24f4abd-e82a-4b69-9c55-06d79ffe3a0c&quot;,&quot;title&quot;:&quot;Deve validar padrões de email incorreto&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar login credenciais incorretas&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar login credenciais incorretas&quot;,&quot;duration&quot;:9671,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;medium&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, &#x27;<EMAIL>&#x27;);\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, &#x27;123456Uds@&#x27;);\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait(&#x27;@login&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 401);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;3c6721ab-c767-46bf-a72d-fe5937f5c28f&quot;,&quot;parentUUID&quot;:&quot;f24f4abd-e82a-4b69-9c55-06d79ffe3a0c&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar qtd mínima de caracteres no email&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres no email&quot;,&quot;duration&quot;:5962,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;medium&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, &#x27;qa&#x27;);\ncy.get(_locators.default.MENSAGENS_ERRO.EMAIL_INVÁLIDO).should(&#x27;have.text&#x27;, _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should(&#x27;have.text&#x27;, &#x27;Entrar&#x27;).and(&#x27;be.visible&#x27;).and(&#x27;be.disabled&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;27ed92e6-d8b9-406e-996c-c5d085729362&quot;,&quot;parentUUID&quot;:&quot;f24f4abd-e82a-4b69-9c55-06d79ffe3a0c&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar qtd mínima de caracteres na senha&quot;,&quot;fullTitle&quot;:&quot;Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres na senha&quot;,&quot;duration&quot;:7112,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;medium&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.typeText(_locators.default.LOGIN.SENHA_CAMPO, &#x27;1234567&#x27;);\ncy.get(_locators.default.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA).should(&#x27;have.text&#x27;, _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should(&#x27;have.text&#x27;, &#x27;Entrar&#x27;).and(&#x27;be.visible&#x27;).and(&#x27;be.disabled&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6586f1a4-72de-4e71-8a49-36e0722f38a2&quot;,&quot;parentUUID&quot;:&quot;f24f4abd-e82a-4b69-9c55-06d79ffe3a0c&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;3c6721ab-c767-46bf-a72d-fe5937f5c28f&quot;,&quot;27ed92e6-d8b9-406e-996c-c5d085729362&quot;,&quot;6586f1a4-72de-4e71-8a49-36e0722f38a2&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:22745,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[&quot;5629ccb7-ce8c-46b7-aaad-4c8744a06069&quot;,&quot;276aca10-8f6a-4c36-b427-da9fa71c35b9&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:21463,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;aece1f81-3c91-4e47-9c0a-f18f0bc7b131&quot;,&quot;title&quot;:&quot;Deve validar o logout&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve realizar o logout&quot;,&quot;fullTitle&quot;:&quot;Deve validar o logout Deve realizar o logout&quot;,&quot;duration&quot;:40498,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;//Validando avatar\ncy.isVisible(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;);\n//Abrindo opções do usuário\ncy.clickMouse(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;);\n//Clicando em sair\ncy.contains(&#x27;section.d_flex &gt; div&#x27;, &#x27;Sair&#x27;).should(&#x27;be.visible&#x27;).click();\ncy.wait(&#x27;@logout&#x27;).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 200);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;9524c31b-06be-4907-b055-6ed65ff77908&quot;,&quot;parentUUID&quot;:&quot;aece1f81-3c91-4e47-9c0a-f18f0bc7b131&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;9524c31b-06be-4907-b055-6ed65ff77908&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:40498,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;overwrite&quot;:true,&quot;html&quot;:true,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;cypress\\results\\assets&quot;,&quot;jsonFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\LoginLogout-report-2025-08-13T202510-0300.json&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\LoginLogout-report-2025-08-13T202510-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>
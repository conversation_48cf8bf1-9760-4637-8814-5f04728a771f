{"uuid": "46e2bb48-1380-45f2-9c63-b81672679088", "historyId": "9071fda04b651649fa0557c70daaab6a", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518969483, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf POST", "stop": 1755518969483}], "attachments": [], "parameters": [], "start": 1755518968977, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 33 more characters, body: { cpf: '53655184972' } }\")", "stop": 1755518969483}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"data\": {\n    \"cpf\": \"53655184972\",\n    \"maskedName\": \"LUIMAR D* A****** C******\",\n    \"birthDate\": \"**/**/1965\"\n  }\n}"}], "start": 1755518969503, "name": "assert expected **{ Object (data) }** to have property **data**", "stop": 1755518969503}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"cpf\": \"53655184972\",\n  \"maskedName\": \"LUIMAR D* A****** C******\",\n  \"birthDate\": \"**/**/1965\"\n}"}], "start": 1755518969503, "name": "assert expected **{ Object (cpf, maskedName, ...) }** to have property **cpf**", "stop": 1755518969503}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "53655184972"}, {"name": "expected", "value": "53655184972"}], "start": 1755518969504, "name": "assert expected **{ Object (cpf, maskedName, ...) }** to have property **cpf** of **'53655184972'**", "stop": 1755518969504}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"cpf\": \"53655184972\",\n  \"maskedName\": \"LUIMAR D* A****** C******\",\n  \"birthDate\": \"**/**/1965\"\n}"}], "start": 1755518969504, "name": "assert expected **{ Object (cpf, maskedName, ...) }** to have property **maskedName**", "stop": 1755518969504}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "LUIMAR D* A****** C******"}], "start": 1755518969504, "name": "assert expected **LUIMAR D* A****** C******** to match /^\\w+/", "stop": 1755518969504}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"cpf\": \"53655184972\",\n  \"maskedName\": \"LUIMAR D* A****** C******\",\n  \"birthDate\": \"**/**/1965\"\n}"}], "start": 1755518969504, "name": "assert expected **{ Object (cpf, maskedName, ...) }** to have property **birthDate**", "stop": 1755518969504}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "**/**/1965"}], "start": 1755518969505, "name": "assert expected ****/**/1965** not to be empty", "stop": 1755518969505}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 200}, {"name": "expected", "value": 200}], "start": 1755518969505, "name": "assert expected **200** to equal **200**", "stop": 1755518969505}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518968942, "name": "Deve retornar 200 e os dados mascarados do usuário", "fullName": "Deve retornar 200 e os dados mascarados do usuário", "stop": 1755518969509}
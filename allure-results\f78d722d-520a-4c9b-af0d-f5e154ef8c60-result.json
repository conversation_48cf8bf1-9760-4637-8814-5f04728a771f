{"uuid": "f78d722d-520a-4c9b-af0d-f5e154ef8c60", "historyId": "ec8f0b67a136f32517f5c7603ca8e705", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968887, "name": "intercept (\"POST\"; \"**/validate/cpf\")", "stop": 1755518968893}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968893, "name": "as (\"validateCpf\")", "stop": 1755518968895}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968897, "name": "Enviar requisição para validar CPF já cadastrado", "stop": 1755518968899}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}, {"name": "epic", "value": "Backend"}, {"name": "feature", "value": "Validação de CPF"}, {"name": "story", "value": "CPF Duplicado"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "api"}, {"name": "tag", "value": "validation"}, {"name": "tag", "value": "cpf"}], "links": [{"name": "JIRA-456", "url": "https://jira.company.com/browse/{}undefined", "type": "issue"}, {"name": "TC-002", "url": "https://testmanagement.company.com/testcase/{}undefined", "type": "tms"}], "start": 1755518968848, "name": "Deve retornar 400 quando CPF já está cadastrado", "fullName": "Deve retornar 400 quando CPF já está cadastrado", "description": "Valida que a API retorna erro 400 quando um CPF já cadastrado é enviado", "stop": 1755518968899}
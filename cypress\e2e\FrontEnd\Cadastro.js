/// <reference types="cypress" />

import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

describe('Cadastro', () => {
    it('Deve validar elementos no cadastro via botão do header', () => {
        blockTrackers();

        cy.allure().step('Navegar para a tela de cadastro via header', () => {
            cy.navigateToRegistration('header')
        });

        cy.allure().step('Validar todos os elementos da tela de cadastro', () => {
            cy.validateRegistrationPageElements()
        });
    })
    it('Deve validar elementos no cadastro via botão da home', () => {
        // Anotações do Allure Report
        cy.allure()
            .epic('Frontend')
            .feature('Cadastro de Usuário')
            .story('Validação de Elementos da Tela')
            .severity('normal')
            .tag('ui', 'cadastro', 'home')
            .description('Valida se todos os elementos da tela de cadastro estão visíveis quando acessada via botão da página inicial');

        blockTrackers();

        cy.allure().step('Navegar para a tela de cadastro via botão da home', () => {
            cy.navigateToRegistration('home')
        });

        cy.allure().step('Validar todos os elementos da tela de cadastro', () => {
            cy.validateRegistrationPageElements()
        });
    })
    it('Deve retornar 400 quando CPF já está cadastrado', () => {
        // Anotações do Allure Report
        cy.allure()
            .epic('Backend')
            .feature('Validação de CPF')
            .story('CPF Duplicado')
            .severity('critical')
            .tag('api', 'validation', 'cpf')
            .description('Valida que a API retorna erro 400 quando um CPF já cadastrado é enviado')
            .issue('JIRA-456')
            .tms('TC-002');

        cy.intercept('POST', '**/validate/cpf').as('validateCpf')

        const requestBody = { cpf: "11983538647" };
        const url = 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/cpf';

        cy.allure().step('Enviar requisição para validar CPF já cadastrado', () => {
            cy.api({
                method: 'POST',
                url: url,
                body: requestBody,
                failOnStatusCode: false
            }).then((response) => {
                // Adiciona detalhes da API ao Allure
                cy.allureApiStep('Validação de CPF duplicado', 'POST', url, requestBody, response);

                cy.allure().step('Validar resposta da API', () => {
                    expect(response.status).to.eq(400)
                    expect(response.body).to.have.property('code', '100762')
                    expect(response.body).to.have.property('message', 'CPF já está cadastrado.')
                });
            })
        });
    })
    it('Deve retornar 200 e os dados mascarados do usuário', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf',
            body: {
                cpf: "53655184972"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body).to.have.property('data')
            expect(response.body.data).to.have.property('cpf', '53655184972')
            expect(response.body.data).to.have.property('maskedName').and.to.match(/^\w+/)
            expect(response.body.data).to.have.property('birthDate').and.to.not.be.empty
        })
    })
    it('Deve retornar 422 e mensagem de documento inválido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf',
            failOnStatusCode: false,
            body: {
                cpf: "80807788090"
            }
        }).then((response) => {
            expect(response.status).to.eq(422)
            expect(response.body).to.have.property('code', 'INVALID_DOCUMENT')
            expect(response.body).to.have.property('message', 'O documento solicitado não está em um formato válido.')
        })
    })
    it('Deve retornar 400 e mensagem ao enviar e-mail inválido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/email',
            failOnStatusCode: false,
            body: {
                email: 'teste'
            }
        }).then(({ status, body, headers }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message').and.match(/Dados inválidos/i)
            expect(headers).to.have.property('content-type').and.match(/application\/json/i)
        })
    })
    it('Deve retornar que o e-mail já está cadastrado', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/email',
            body: {
                email: "<EMAIL>"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body.data.isUnique).to.be.false
        })
    })
    it('Deve validar número de telefone com sucesso', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            body: {
                isdCode: "55",
                telephoneNumber: "41121321231"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body.data.isValid).to.be.true
        })
    })
    it('Deve retornar 400 quando o número de telefone já está cadastrado', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "55",
                telephoneNumber: "11111111111"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100294')
            expect(body).to.have.property('message', 'Esse número de celular já está cadastrado.')
        })
    })
    it('Deve retornar 400 quando o número de telefone não é válido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "55",
                telephoneNumber: "teste"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message', 'Invalid Brazilian telephone number')
        })
    })
    it('Deve retornar 400 quando o codigo internacional não é válido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "XX",
                telephoneNumber: "teste"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message', 'Invalid international telephone number')
        })
    })
    //it('Cadastrar usuario', () => { }) 
})
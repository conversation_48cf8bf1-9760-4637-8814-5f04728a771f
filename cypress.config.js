const { defineConfig } = require("cypress");
const allureWriter = require('@shelex/cypress-allure-plugin/writer');

module.exports = defineConfig({
  //chromeWebSecurity: false,
  video: false,
  screenshotOnRunFailure: false,
  trashAssetsBeforeRuns: true,
  retries: 0,
  viewportWidth: 1900,
  viewportHeight: 1000,
  //projectId: "",
  e2e: {
    chromeWebSecurity: false,

    setupNodeEvents(on, config) {
      // Configuração do Allure Report
      allureWriter(on, config);

      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.family === 'chromium' && browser.name !== 'electron') {
          launchOptions.args.push(
            '--disable-blink-features=AutomationControlled',
            '--disable-infobars'
          );
        }
        return launchOptions;
      });

      return config;
    },
    //baseUrl: '',
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}',
    env: {
      allure: true,
      allureReuseAfterSpec: true,
      allureResultsPath: "allure-results",
      allureAttachRequests: true,
      allureLogCypress: true,
      allureSkipSteps: "wrap,within,should,and,invoke,its,then,each,apply,spread,scrollIntoView,trigger,click,type,clear,check,uncheck,select,selectFile,focus,blur,submit,rightclick,dblclick,hover,selectFile,drag,drop,scrollTo,wait,log,task,exec,readFile,writeFile,fixture,screenshot,session,origin,intercept,request,visit,reload,go,url,title,hash,location,window,document,get,contains,find,filter,not,first,last,eq,next,prev,nextAll,prevAll,nextUntil,prevUntil,siblings,children,parent,parents,parentsUntil,closest,as,debug,pause,end"
    }
  },
  // Mantendo o mochawesome como reporter secundário
  reporter: "mochawesome",
  reporterOptions: {
    reportDir: "cypress/results",
    overwrite: true,
    html: true,
    json: true,
    reportFilename: '[name]-report-[datetime]'
  },
});

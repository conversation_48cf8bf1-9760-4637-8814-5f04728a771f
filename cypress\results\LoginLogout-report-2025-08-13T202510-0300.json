{"stats": {"suites": 3, "tests": 6, "passes": 6, "pending": 0, "failures": 0, "start": "2025-08-13T23:23:26.340Z", "end": "2025-08-13T23:25:10.825Z", "duration": 104485, "testsRegistered": 6, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "57d29c6a-f8e6-4d5b-91b2-3afcc22487ab", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "file": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "80b07b92-2070-471d-b59a-64e7575f67e1", "title": "<PERSON>e validar o login", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Validando elementos da tela", "fullTitle": "Deve validar o login Validando elementos da tela", "timedOut": null, "duration": 7697, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Suporte\ncy.validateText('span.d_block', 'Suporte');\n//Valida logo do cliente\ncy.get(_locators.default.LOGIN.LOGO_CLIENTE).should('be.visible').and('have.attr', 'src').and('match', /\\/logo-estrelabet\\.svg$/);\n//Validação do título e subtítulo\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.LOGIN_PAGE.TITLE);\n//Validando campo email, título e placeholder\ncy.validateText(_locators.default.LOGIN.EMAIL_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.EMAIL_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL);\n//Validando campo senha, título e placeholder\ncy.validateText(_locators.default.LOGIN.SENHA_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.SENHA_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA);\n// Digitar senha\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type('123456').should('have.attr', 'type', 'password'); // Verifica que está oculta\n// Clicar no botão de mostrar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).should('be.visible').and('exist').click();\n// Verificar que a senha está visível\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'text');\n// Clicar novamente para ocultar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).click();\n// Verificar que a senha voltou a ser oculta\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'password');\n//Botão esqueci a senha\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "5629ccb7-ce8c-46b7-aaad-4c8744a06069", "parentUUID": "80b07b92-2070-471d-b59a-64e7575f67e1", "isHook": false, "skipped": false}, {"title": "Deve realizar o login com sucesso", "fullTitle": "Deve validar o login Deve realizar o login com sucesso", "timedOut": null, "duration": 13766, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "const username = Cypress.env('user_name');\nconst password = Cypress.env('user_password');\nexpect(username, 'Nome de usuário').to.be.a('string').and.not.be.empty;\nif (typeof password !== 'string' || !password) {\n  throw new Error('O valor senha está ausente, inclua a senha usando o cy.env');\n}\ncy.get(_locators.default.LOGIN.EMAIL_CAMPO).type(username).should('have.value', username);\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(password, {\n  log: false\n}).should(el$ => {\n  if (el$.val() !== password) {\n    throw new Error('Valor diferente da senha digitada');\n  }\n});\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 200);\ncy.get('@login').then(({\n  request,\n  response\n}) => {\n  expect(request.method).to.equal('POST');\n  cy.isVisible('.nebulosa-avatar__fallback:eq(2)');\n});", "err": {}, "uuid": "276aca10-8f6a-4c36-b427-da9fa71c35b9", "parentUUID": "80b07b92-2070-471d-b59a-64e7575f67e1", "isHook": false, "skipped": false}], "suites": [{"uuid": "f24f4abd-e82a-4b69-9c55-06d79ffe3a0c", "title": "Deve validar padrões de email incorreto", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve validar login credenciais incorretas", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar login credenciais incorretas", "timedOut": null, "duration": 9671, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, '<EMAIL>');\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '123456Uds@');\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 401);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO);", "err": {}, "uuid": "3c6721ab-c767-46bf-a72d-fe5937f5c28f", "parentUUID": "f24f4abd-e82a-4b69-9c55-06d79ffe3a0c", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres no email", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres no email", "timedOut": null, "duration": 5962, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, 'qa');\ncy.get(_locators.default.MENSAGENS_ERRO.EMAIL_INVÁLIDO).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "27ed92e6-d8b9-406e-996c-c5d085729362", "parentUUID": "f24f4abd-e82a-4b69-9c55-06d79ffe3a0c", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres na senha", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres na senha", "timedOut": null, "duration": 7112, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '1234567');\ncy.get(_locators.default.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "6586f1a4-72de-4e71-8a49-36e0722f38a2", "parentUUID": "f24f4abd-e82a-4b69-9c55-06d79ffe3a0c", "isHook": false, "skipped": false}], "suites": [], "passes": ["3c6721ab-c767-46bf-a72d-fe5937f5c28f", "27ed92e6-d8b9-406e-996c-c5d085729362", "6586f1a4-72de-4e71-8a49-36e0722f38a2"], "failures": [], "pending": [], "skipped": [], "duration": 22745, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": ["5629ccb7-ce8c-46b7-aaad-4c8744a06069", "276aca10-8f6a-4c36-b427-da9fa71c35b9"], "failures": [], "pending": [], "skipped": [], "duration": 21463, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "aece1f81-3c91-4e47-9c0a-f18f0bc7b131", "title": "<PERSON>e validar o logout", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve realizar o logout", "fullTitle": "Deve validar o logout Deve realizar o logout", "timedOut": null, "duration": 40498, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Validando avatar\ncy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Abrindo opções do usuário\ncy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Clicando em sair\ncy.contains('section.d_flex > div', 'Sair').should('be.visible').click();\ncy.wait('@logout').its('response.statusCode').should('eq', 200);", "err": {}, "uuid": "9524c31b-06be-4907-b055-6ed65ff77908", "parentUUID": "aece1f81-3c91-4e47-9c0a-f18f0bc7b131", "isHook": false, "skipped": false}], "suites": [], "passes": ["9524c31b-06be-4907-b055-6ed65ff77908"], "failures": [], "pending": [], "skipped": [], "duration": 40498, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
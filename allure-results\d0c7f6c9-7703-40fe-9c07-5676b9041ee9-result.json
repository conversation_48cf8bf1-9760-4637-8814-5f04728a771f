{"uuid": "d0c7f6c9-7703-40fe-9c07-5676b9041ee9", "historyId": "d146637ef87f3bdbf87351e8ab3749e8", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518970151, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/validate/email POST", "stop": 1755518970151}], "attachments": [], "parameters": [], "start": 1755518970050, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 19 more characters, failOnStatusCode: false, body: { email: 'teste' } }\")", "stop": 1755518970152}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100164\",\n  \"message\": \"Dados inválidos. Verifique e tente novamente.\"\n}"}], "start": 1755518970172, "name": "assert expected **{ Object (code, message) }** to have property **code**", "stop": 1755518970172}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "100164"}, {"name": "expected", "value": "100164"}], "start": 1755518970172, "name": "assert expected **{ Object (code, message) }** to have property **code** of **'100164'**", "stop": 1755518970172}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100164\",\n  \"message\": \"Dados inválidos. Verifique e tente novamente.\"\n}"}], "start": 1755518970172, "name": "assert expected **{ Object (code, message) }** to have property **message**", "stop": 1755518970172}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "Dados inválidos. Verifique e tente novamente."}], "start": 1755518970172, "name": "assert expected **Dad<PERSON> inválidos. Verifique e tente novamente.** to match /Dados inválidos/i", "stop": 1755518970172}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"date\": \"Mon, 18 Aug 2025 12:09:30 GMT\",\n  \"content-type\": \"application/json; charset=utf-8\",\n  \"content-length\": \"76\",\n  \"connection\": \"keep-alive\",\n  \"cf-ray\": \"97114d7b9fccae1a-GRU\",\n  \"vary\": \"Origin\",\n  \"x-forwarded-for\": \"189.114.190.42\",\n  \"x-request-id\": \"adce08ab-0d18-4544-be75-662fae759fd7\",\n  \"cf-cache-status\": \"DYNAMIC\",\n  \"set-cookie\": [\n    \"__cf_bm=fxltvohDLmedC.TPeHw7CrzLvhf9ITE1xQc3dEsaEMM-1755518970-1.0.1.1-t5d2KTLDV3fp8.rxN3O1_ECTZP6BLGF_FIOq8vBiqGZINRUYjdZbnLkS6DBzp1dTfy1KbR_zMr28q9fc_cVIsEgPFdncFM.sL0sDUL5jQw8; path=/; expires=Mon, 18-Aug-25 12:39:30 GMT; domain=.estrelabet.bet.br; HttpOnly; Secure; SameSite=None\"\n  ],\n  \"strict-transport-security\": \"max-age=0; includeSubDomains\",\n  \"x-content-type-options\": \"nosniff\",\n  \"server\": \"cloudflare\",\n  \"alt-svc\": \"h3=\\\":443\\\"; ma=86400\"\n}"}], "start": 1755518970173, "name": "assert expected **{ Object (date, content-type, ...) }** to have property **content-type**", "stop": 1755518970173}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "application/json; charset=utf-8"}], "start": 1755518970173, "name": "assert expected **application/json; charset=utf-8** to match /application\\/json/i", "stop": 1755518970173}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 400}], "start": 1755518970173, "name": "assert expected **400** to equal **400**", "stop": 1755518970173}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518969996, "name": "Deve retornar 400 e mensagem ao enviar e-mail inválido", "fullName": "Deve retornar 400 e mensagem ao enviar e-mail inválido", "stop": 1755518970177}
{"stats": {"suites": 3, "tests": 6, "passes": 6, "pending": 0, "failures": 0, "start": "2025-08-13T23:39:36.268Z", "end": "2025-08-13T23:40:52.566Z", "duration": 76298, "testsRegistered": 6, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "714b88c0-90af-4d16-8be3-c6c0f46c142a", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "file": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "def3bb18-e14a-4d9c-ac5e-3b2960f573db", "title": "<PERSON>e validar o login", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Validando elementos da tela", "fullTitle": "Deve validar o login Validando elementos da tela", "timedOut": null, "duration": 12920, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Suporte\ncy.validateText('span.d_block', 'Suporte');\n//Valida logo do cliente\ncy.get(_locators.default.LOGIN.LOGO_CLIENTE).should('be.visible').and('have.attr', 'src').and('match', /\\/logo-estrelabet\\.svg$/);\n//Validação do título e subtítulo\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.LOGIN_PAGE.TITLE);\n//Validando campo email, título e placeholder\ncy.validateText(_locators.default.LOGIN.EMAIL_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.EMAIL_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL);\n//Validando campo senha, título e placeholder\ncy.validateText(_locators.default.LOGIN.SENHA_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.SENHA_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA);\n// Digitar senha\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type('123456').should('have.attr', 'type', 'password'); // Verifica que está oculta\n// Clicar no botão de mostrar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).should('be.visible').and('exist').click();\n// Verificar que a senha está visível\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'text');\n// Clicar novamente para ocultar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).click();\n// Verificar que a senha voltou a ser oculta\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'password');\n//Botão esqueci a senha\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "60f1a986-0bf5-4f0d-b60e-0746bcac60df", "parentUUID": "def3bb18-e14a-4d9c-ac5e-3b2960f573db", "isHook": false, "skipped": false}, {"title": "Deve realizar o login com sucesso", "fullTitle": "Deve validar o login Deve realizar o login com sucesso", "timedOut": null, "duration": 14223, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "const username = Cypress.env('user_name');\nconst password = Cypress.env('user_password');\nexpect(username, 'Nome de usuário').to.be.a('string').and.not.be.empty;\nif (typeof password !== 'string' || !password) {\n  throw new Error('O valor senha está ausente, inclua a senha usando o cy.env');\n}\ncy.get(_locators.default.LOGIN.EMAIL_CAMPO).type(username).should('have.value', username);\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(password, {\n  log: false\n}).should(el$ => {\n  if (el$.val() !== password) {\n    throw new Error('Valor diferente da senha digitada');\n  }\n});\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 200);\ncy.get('@login').then(({\n  request,\n  response\n}) => {\n  expect(request.method).to.equal('POST');\n  cy.isVisible('.nebulosa-avatar__fallback:eq(2)');\n});", "err": {}, "uuid": "b36ba3ba-bb44-4600-9028-df0b5113ea37", "parentUUID": "def3bb18-e14a-4d9c-ac5e-3b2960f573db", "isHook": false, "skipped": false}], "suites": [{"uuid": "4c88c0ee-b5b1-4afa-8190-943e1071d9f0", "title": "Deve validar padrões de email incorreto", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve validar login credenciais incorretas", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar login credenciais incorretas", "timedOut": null, "duration": 16274, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, '<EMAIL>');\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '123456Uds@');\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 401);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO);", "err": {}, "uuid": "56beb42a-3c4c-4861-9406-5721843610fa", "parentUUID": "4c88c0ee-b5b1-4afa-8190-943e1071d9f0", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres no email", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres no email", "timedOut": null, "duration": 3887, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, 'qa');\ncy.get(_locators.default.MENSAGENS_ERRO.EMAIL_INVÁLIDO).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "a802b9fc-7514-43d7-b177-991dea522967", "parentUUID": "4c88c0ee-b5b1-4afa-8190-943e1071d9f0", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres na senha", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres na senha", "timedOut": null, "duration": 4369, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '1234567');\ncy.get(_locators.default.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "e48231e9-9ab2-4425-b4dc-0324786e5ad7", "parentUUID": "4c88c0ee-b5b1-4afa-8190-943e1071d9f0", "isHook": false, "skipped": false}], "suites": [], "passes": ["56beb42a-3c4c-4861-9406-5721843610fa", "a802b9fc-7514-43d7-b177-991dea522967", "e48231e9-9ab2-4425-b4dc-0324786e5ad7"], "failures": [], "pending": [], "skipped": [], "duration": 24530, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": ["60f1a986-0bf5-4f0d-b60e-0746bcac60df", "b36ba3ba-bb44-4600-9028-df0b5113ea37"], "failures": [], "pending": [], "skipped": [], "duration": 27143, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "87a6bcc8-f829-450b-ae8d-fb8f3dba8be9", "title": "<PERSON>e validar o logout", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve realizar o logout", "fullTitle": "Deve validar o logout Deve realizar o logout", "timedOut": null, "duration": 14633, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Validando avatar\ncy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Abrindo opções do usuário\ncy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Clicando em sair\ncy.contains('section.d_flex > div', 'Sair').should('be.visible').click();\ncy.wait('@logout').its('response.statusCode').should('eq', 200);", "err": {}, "uuid": "4e3a99e6-fdce-484b-8001-7a07232d8e0b", "parentUUID": "87a6bcc8-f829-450b-ae8d-fb8f3dba8be9", "isHook": false, "skipped": false}], "suites": [], "passes": ["4e3a99e6-fdce-484b-8001-7a07232d8e0b"], "failures": [], "pending": [], "skipped": [], "duration": 14633, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
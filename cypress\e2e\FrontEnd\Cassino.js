/// <reference types="cypress" />

import loc from '../../support/locators'
import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

describe('Deve abrir a página de cassino', () => {
    beforeEach(() => {
        // Bloqueia terceiros e aplica otimizações de performance
        blockTrackers();

        cy.loginToAuth0(Cypress.env('user_name'), Cypress.env('user_password'))

        cy.intercept({
            method: 'GET',
            url: 'https://hml.estrelabet.bet.br/cassino-online',
        }).as('getCassino')

        //cy.get('.nebulosa-avatar__fallback:eq(2)', { timeout: 18000 })
        //  .should('have.text', 'AC')
    });

    it('Acessa pagina de Cassino e valida elementos', () => {
        cy.contains('a', 'Cassino')
        cy.contains('a', 'Cassino').click();

        // Valida o botão de busca (ícone de lupa)
        cy.get('a[href="/jogos/busca"]')
            .should('be.visible');

        // Lista de labels esperadas
        const botoes = [
            'Inicial',
            'Torneios',
            'Slots',
            'Crash Games',
            'Comprar Bônus',
            'Novos Jogos',
            'Jackpots',
            'Mais'
        ];

        // Valida cada botão
        botoes.forEach((label) => {
            cy.contains('.nebulosa-button__buttonLabel', label)
                .should('be.visible');
        });

        cy.clickByContains('.nebulosa-button__buttonLabel', 'Mais')

        // Lista de labels esperadas
        const categorias = [
            'Populares',
            'Jogos de Fortuna',
            'Games Global',
            'Estrela Indica',
            'Novos Jogos',
            'Pragmatic Play',
            'Missão Lucky Rush',
            'Crash Games',
            'Plinko',
            'Comprar Bônus',
            'Slots',
            'Jogos Ao Vivo',
            'Jackpots',
            'Em breve',
            'Provedores'
        ];

        // Valida cada label visível
        categorias.forEach((label) => {
            cy.contains('li', label).should('be.visible');
        });

        // Fecha o modal (botão X no header)
        cy.get('.nebulosa-modal__HeaderRight .nebulosa-modal__IconAction')
            .click();

        // Valida que o modal fechou
        cy.get('[role="dialog"]').should('not.exist');

        cy.clickByContains('div.d_flex > div > div > div > h2', 'Populares');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Jogos de Fortuna');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Estrela Indica');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Novos Jogos');
        cy.clickByContains(':nth-child(5) > .ov-y_hidden > .jc_space-between > .desktop\\:ai_center > .jc_start', 'Mais premiados');
        cy.clickByContains(':nth-child(6) > .ov-y_hidden > .jc_space-between > .desktop\\:ai_center > .jc_start', 'Menos premiados');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Pragmatic Play');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Crash Games');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Comprar Bônus');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Slots');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Jogos Ao Vivo');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Jackpots');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Em breve');
        cy.clickByContains('div.d_flex > div > div > div > h2', 'Provedores de jogos');

    });

    it('Acessa pagina de Cassino e acessar um jogo', () => {

        cy.contains('a', 'Cassino')
        cy.contains('a', 'Cassino').click();

        // Acessar jogos
        cy.get('img[alt="Aviator-game-img"]').click()

        //Caso o usuario nao tenha verificado a identidade, clica no botão "Não quero verificar agora"
        cy.get('body').then(($body) => {
            // Verifica se o botão existe
            if ($body.find('button:contains("Não quero verificar agora")').length) {
                cy.contains('button', 'Não quero verificar agora').click();
            } else {
                cy.log('Modal não apareceu, continuando o teste...');
            }
        });

    });

    it('Valida elementos do footer', () => {
        // "Aposte"
        cy.isVisible('.StaticFooter_footer-links-section__0Fi_h', 'Aposte')

        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/esportes#/overview"]', 'Apostas esportivas')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/gameplay/fortune-tiger"]', 'Fortune Tiger')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/gameplay/fortune-rabbit"]', 'Fortune Rabbit')

        // "Links úteis"
        cy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Links úteis')

        cy.isVisible('[href="https://comunidade.estrelabet.com/"]', 'Comunidade')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/promotions/all"]', 'Promoções')

        // "Regras"
        cy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Regras')

        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/politica/termos-e-condicoes"]', 'Termos e Condições Gerais')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/page/responsible-gaming"]', 'Jogo responsável')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/policy/sports-betting-rules"]', 'Regras de apostas esportivas')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/policy/bonus-rules"]', 'Termos e condições gerais de bônus')
        cy.isVisible('[href="https://www.estrelabet.bet.br/pb/policy/privacy-policy"]', 'Política de privacidade')

        // "Suporte"
        cy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Suporte')

        cy.isVisible('[href="https://estrelabet.zendesk.com/hc"]', 'Central de ajuda')
        cy.isVisible('[href="tel:0800 000 4546"]', '0800 000 4546')
        cy.isVisible('[href="mailto:<EMAIL>"]', '<EMAIL>')

        // "Outros"
        cy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Outros')

        cy.isVisible('[href="https://estrela-bet.atlassian.net/helpcenter/ouvidoria/"]', 'Ouvidoria')
        cy.isVisible('[href="https://consumidor2.procon.sp.gov.br/login"]', 'Procon')
        cy.isVisible('[href="https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm"]', 'Código de Defesa do Consumidor')

    });
});

const { defineConfig } = require("cypress");

module.exports = defineConfig({
  //chromeWebSecurity: false,
  video: false,
  screenshotOnRunFailure: false,
  trashAssetsBeforeRuns: true,
  retries: 0,
  viewportWidth: 1900,
  viewportHeight: 1000,
  //projectId: "",
  e2e: {
    chromeWebSecurity: false,

    setupNodeEvents(on, config) {
      on('before:browser:launch', (browser = {}, launchOptions) => {
        if (browser.family === 'chromium' && browser.name !== 'electron') {
          launchOptions.args.push(
            '--disable-blink-features=AutomationControlled',
            '--disable-infobars'
          );
        }
        return launchOptions;
      });

    },
    //baseUrl: '',
    specPattern: 'cypress/e2e/**/*.{js,jsx,ts,tsx}'
  },
  reporter: "mochawesome",
  reporterOptions: {
    reportDir: "cypress/results",
    overwrite: true,
    html: true,
    json: true,
    reportFilename: '[name]-report-[datetime]'

  },
});

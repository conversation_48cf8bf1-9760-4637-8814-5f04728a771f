{"uuid": "4fcdf6e6-2f2b-4990-adb3-6df6b72caef0", "historyId": "d7d2ef05e4ab9303d9f885629124d5ec", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968811, "name": "Navegar para a tela de cadastro via botão da home", "stop": 1755518968812}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968812, "name": "Validar todos os elementos da tela de cadastro", "stop": 1755518968813}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}, {"name": "epic", "value": "Frontend"}, {"name": "feature", "value": "Cadastro de Usuário"}, {"name": "story", "value": "Validação de Elementos da Tela"}, {"name": "severity", "value": "normal"}, {"name": "tag", "value": "ui"}, {"name": "tag", "value": "cadastro"}, {"name": "tag", "value": "home"}], "links": [], "start": 1755518968632, "name": "Deve validar elementos no cadastro via botão da home", "fullName": "Deve validar elementos no cadastro via botão da home", "description": "Valida se todos os elementos da tela de cadastro estão visíveis quando acessada via botão da página inicial", "stop": 1755518968813}
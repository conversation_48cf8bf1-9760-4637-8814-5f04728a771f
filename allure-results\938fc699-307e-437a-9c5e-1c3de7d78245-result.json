{"uuid": "938fc699-307e-437a-9c5e-1c3de7d78245", "historyId": "a8e9c628b4ef41ad74917de9c5d3334e", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518971127, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber POST", "stop": 1755518971127}], "attachments": [], "parameters": [], "start": 1755518971055, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 29 more characters, failOnStatusCode: false, body: { isdCode: '55', telephoneNumber: 'teste' } }\")", "stop": 1755518971127}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100164\",\n  \"message\": \"Invalid Brazilian telephone number\"\n}"}], "start": 1755518971136, "name": "assert expected **{ Object (code, message) }** to have property **code**", "stop": 1755518971136}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "100164"}, {"name": "expected", "value": "100164"}], "start": 1755518971137, "name": "assert expected **{ Object (code, message) }** to have property **code** of **'100164'**", "stop": 1755518971137}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100164\",\n  \"message\": \"Invalid Brazilian telephone number\"\n}"}], "start": 1755518971137, "name": "assert expected **{ Object (code, message) }** to have property **message**", "stop": 1755518971137}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "Invalid Brazilian telephone number"}, {"name": "expected", "value": "Invalid Brazilian telephone number"}], "start": 1755518971138, "name": "assert expected **{ Object (code, message) }** to have property **message** of **Invalid Brazilian telephone number**", "stop": 1755518971138}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 400}], "start": 1755518971138, "name": "assert expected **400** to equal **400**", "stop": 1755518971138}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518971007, "name": "Deve retornar 400 quando o número de telefone não é válido", "fullName": "Deve retornar 400 quando o número de telefone não é válido", "stop": 1755518971143}
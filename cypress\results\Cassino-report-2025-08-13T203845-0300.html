<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:1,&quot;tests&quot;:3,&quot;passes&quot;:3,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-08-13T23:37:14.519Z&quot;,&quot;end&quot;:&quot;2025-08-13T23:38:45.924Z&quot;,&quot;duration&quot;:91405,&quot;testsRegistered&quot;:3,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;4a14c0ae-df4b-4f41-b513-c5c37a710b17&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Cassino.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Cassino.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;22590d79-8f12-4f05-8ebb-8f68ed8ed7aa&quot;,&quot;title&quot;:&quot;Deve abrir a página de cassino&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Acessa pagina de Cassino e valida elementos&quot;,&quot;fullTitle&quot;:&quot;Deve abrir a página de cassino Acessa pagina de Cassino e valida elementos&quot;,&quot;duration&quot;:46884,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.contains(&#x27;a&#x27;, &#x27;Cassino&#x27;);\ncy.contains(&#x27;a&#x27;, &#x27;Cassino&#x27;).click();\n// Valida o botão de busca (ícone de lupa)\ncy.get(&#x27;a[href=\&quot;/jogos/busca\&quot;]&#x27;).should(&#x27;be.visible&#x27;);\n// Lista de labels esperadas\nconst botoes = [&#x27;Inicial&#x27;, &#x27;Torneios&#x27;, &#x27;Slots&#x27;, &#x27;Crash Games&#x27;, &#x27;Comprar Bônus&#x27;, &#x27;Novos Jogos&#x27;, &#x27;Jackpots&#x27;, &#x27;Mais&#x27;];\n// Valida cada botão\nbotoes.forEach(label =&gt; {\n  cy.contains(&#x27;.nebulosa-button__buttonLabel&#x27;, label).should(&#x27;be.visible&#x27;);\n});\ncy.clickByContains(&#x27;.nebulosa-button__buttonLabel&#x27;, &#x27;Mais&#x27;);\n// Lista de labels esperadas\nconst categorias = [&#x27;Populares&#x27;, &#x27;Jogos de Fortuna&#x27;, &#x27;Games Global&#x27;, &#x27;Estrela Indica&#x27;, &#x27;Novos Jogos&#x27;, &#x27;Pragmatic Play&#x27;, &#x27;Missão Lucky Rush&#x27;, &#x27;Crash Games&#x27;, &#x27;Plinko&#x27;, &#x27;Comprar Bônus&#x27;, &#x27;Slots&#x27;, &#x27;Jogos Ao Vivo&#x27;, &#x27;Jackpots&#x27;, &#x27;Em breve&#x27;, &#x27;Provedores&#x27;];\n// Valida cada label visível\ncategorias.forEach(label =&gt; {\n  cy.contains(&#x27;li&#x27;, label).should(&#x27;be.visible&#x27;);\n});\n// Fecha o modal (botão X no header)\ncy.get(&#x27;.nebulosa-modal__HeaderRight .nebulosa-modal__IconAction&#x27;).click();\n// Valida que o modal fechou\ncy.get(&#x27;[role=\&quot;dialog\&quot;]&#x27;).should(&#x27;not.exist&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Populares&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Jogos de Fortuna&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Estrela Indica&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Novos Jogos&#x27;);\ncy.clickByContains(&#x27;:nth-child(5) &gt; .ov-y_hidden &gt; .jc_space-between &gt; .desktop\\\\:ai_center &gt; .jc_start&#x27;, &#x27;Mais premiados&#x27;);\ncy.clickByContains(&#x27;:nth-child(6) &gt; .ov-y_hidden &gt; .jc_space-between &gt; .desktop\\\\:ai_center &gt; .jc_start&#x27;, &#x27;Menos premiados&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Pragmatic Play&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Crash Games&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Comprar Bônus&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Slots&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Jogos Ao Vivo&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Jackpots&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Em breve&#x27;);\ncy.clickByContains(&#x27;div.d_flex &gt; div &gt; div &gt; div &gt; h2&#x27;, &#x27;Provedores de jogos&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;169b1621-b67e-4b49-ba50-870d4e62b9bc&quot;,&quot;parentUUID&quot;:&quot;22590d79-8f12-4f05-8ebb-8f68ed8ed7aa&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Acessa pagina de Cassino e acessar um jogo&quot;,&quot;fullTitle&quot;:&quot;Deve abrir a página de cassino Acessa pagina de Cassino e acessar um jogo&quot;,&quot;duration&quot;:11726,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.contains(&#x27;a&#x27;, &#x27;Cassino&#x27;);\ncy.contains(&#x27;a&#x27;, &#x27;Cassino&#x27;).click();\n// Acessar jogos\ncy.get(&#x27;img[alt=\&quot;Aviator-game-img\&quot;]&#x27;).click();\n//Caso o usuario nao tenha verificado a identidade, clica no botão \&quot;Não quero verificar agora\&quot;\ncy.get(&#x27;body&#x27;).then($body =&gt; {\n  // Verifica se o botão existe\n  if ($body.find(&#x27;button:contains(\&quot;Não quero verificar agora\&quot;)&#x27;).length) {\n    cy.contains(&#x27;button&#x27;, &#x27;Não quero verificar agora&#x27;).click();\n  } else {\n    cy.log(&#x27;Modal não apareceu, continuando o teste...&#x27;);\n  }\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;247024cd-2b75-446d-ae21-a3e8a7a16508&quot;,&quot;parentUUID&quot;:&quot;22590d79-8f12-4f05-8ebb-8f68ed8ed7aa&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Valida elementos do footer&quot;,&quot;fullTitle&quot;:&quot;Deve abrir a página de cassino Valida elementos do footer&quot;,&quot;duration&quot;:1862,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;// \&quot;Aposte\&quot;\ncy.isVisible(&#x27;.StaticFooter_footer-links-section__0Fi_h&#x27;, &#x27;Aposte&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/esportes#/overview\&quot;]&#x27;, &#x27;Apostas esportivas&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/gameplay/fortune-tiger\&quot;]&#x27;, &#x27;Fortune Tiger&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/gameplay/fortune-rabbit\&quot;]&#x27;, &#x27;Fortune Rabbit&#x27;);\n// \&quot;Links úteis\&quot;\ncy.isVisible(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Links úteis&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://comunidade.estrelabet.com/\&quot;]&#x27;, &#x27;Comunidade&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/promotions/all\&quot;]&#x27;, &#x27;Promoções&#x27;);\n// \&quot;Regras\&quot;\ncy.isVisible(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Regras&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/politica/termos-e-condicoes\&quot;]&#x27;, &#x27;Termos e Condições Gerais&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/page/responsible-gaming\&quot;]&#x27;, &#x27;Jogo responsável&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/policy/sports-betting-rules\&quot;]&#x27;, &#x27;Regras de apostas esportivas&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/policy/bonus-rules\&quot;]&#x27;, &#x27;Termos e condições gerais de bônus&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.estrelabet.bet.br/pb/policy/privacy-policy\&quot;]&#x27;, &#x27;Política de privacidade&#x27;);\n// \&quot;Suporte\&quot;\ncy.isVisible(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Suporte&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://estrelabet.zendesk.com/hc\&quot;]&#x27;, &#x27;Central de ajuda&#x27;);\ncy.isVisible(&#x27;[href=\&quot;tel:0800 000 4546\&quot;]&#x27;, &#x27;0800 000 4546&#x27;);\ncy.isVisible(&#x27;[href=\&quot;mailto:<EMAIL>\&quot;]&#x27;, &#x27;<EMAIL>&#x27;);\n// \&quot;Outros\&quot;\ncy.isVisible(&#x27;.StaticFooter_footer-links-header__U2WL_&#x27;, &#x27;Outros&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://estrela-bet.atlassian.net/helpcenter/ouvidoria/\&quot;]&#x27;, &#x27;Ouvidoria&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://consumidor2.procon.sp.gov.br/login\&quot;]&#x27;, &#x27;Procon&#x27;);\ncy.isVisible(&#x27;[href=\&quot;https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm\&quot;]&#x27;, &#x27;Código de Defesa do Consumidor&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6f5cd5e5-e6a3-458a-8256-0767829ff10a&quot;,&quot;parentUUID&quot;:&quot;22590d79-8f12-4f05-8ebb-8f68ed8ed7aa&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;169b1621-b67e-4b49-ba50-870d4e62b9bc&quot;,&quot;247024cd-2b75-446d-ae21-a3e8a7a16508&quot;,&quot;6f5cd5e5-e6a3-458a-8256-0767829ff10a&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:60472,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;overwrite&quot;:true,&quot;html&quot;:true,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;cypress\\results\\assets&quot;,&quot;jsonFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\Cassino-report-2025-08-13T203845-0300.json&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\Cassino-report-2025-08-13T203845-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>
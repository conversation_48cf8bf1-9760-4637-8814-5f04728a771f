{"uuid": "936b80f5-a9b1-40b4-9aa5-9880c22e122d", "historyId": "22ea9ec5e05dbe25175425541df7e328", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518971291, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber POST", "stop": 1755518971291}], "attachments": [], "parameters": [], "start": 1755518971237, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 29 more characters, failOnStatusCode: false, body: { isdCode: 'XX', telephoneNumber: 'teste' } }\")", "stop": 1755518971291}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100164\",\n  \"message\": \"Invalid international telephone number\"\n}"}], "start": 1755518971296, "name": "assert expected **{ Object (code, message) }** to have property **code**", "stop": 1755518971296}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "100164"}, {"name": "expected", "value": "100164"}], "start": 1755518971296, "name": "assert expected **{ Object (code, message) }** to have property **code** of **'100164'**", "stop": 1755518971296}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "{\n  \"code\": \"100164\",\n  \"message\": \"Invalid international telephone number\"\n}"}], "start": 1755518971296, "name": "assert expected **{ Object (code, message) }** to have property **message**", "stop": 1755518971296}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": "Invalid international telephone number"}, {"name": "expected", "value": "Invalid international telephone number"}], "start": 1755518971296, "name": "assert expected **{ Object (code, message) }** to have property **message** of **Invalid international telephone number**", "stop": 1755518971296}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 400}, {"name": "expected", "value": 400}], "start": 1755518971296, "name": "assert expected **400** to equal **400**", "stop": 1755518971296}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518971210, "name": "Deve retornar 400 quando o codigo internacional não é válido", "fullName": "Deve retornar 400 quando o codigo internacional não é válido", "stop": 1755518971298}
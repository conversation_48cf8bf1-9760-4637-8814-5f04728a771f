{"stats": {"suites": 2, "tests": 2, "passes": 2, "pending": 0, "failures": 0, "start": "2025-08-13T23:25:22.091Z", "end": "2025-08-13T23:26:13.862Z", "duration": 51771, "testsRegistered": 2, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "42465d28-0b9d-4ce2-8c15-73529f9edb10", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\minhaConta.js", "file": "cypress\\e2e\\FrontEnd\\minhaConta.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "4088af73-22aa-4d37-ba6f-3a6309f9011c", "title": "Deve validar opções da conta", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Validando pagina da minha conta", "fullTitle": "Deve validar opções da conta Validando pagina da minha conta", "timedOut": null, "duration": 25258, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Validando avatar\ncy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Abrindo opções do usuário\ncy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Validando opção Minha conta e acessando\ncy.validateText(':nth-child(4) > :nth-child(2) > .fw_regular', 'Minha conta');\ncy.clickMouse(':nth-child(4) > :nth-child(2) > .fw_regular');\n//validando opções de acesso do usuário\ncy.get('[role=\"menuitem\"] > label:eq(0)').should('have.text', 'Conta').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(1)').should('have.text', '<PERSON><PERSON><PERSON>').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(2)').should('have.text', 'Bônus').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(3)').should('have.text', 'Apostas').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(4)').should('have.text', 'Limites').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(5)').should('have.text', 'Segurança').and('be.visible');\ncy.log('Validando modal Minha conta');\n//Titulo e subtítulo da área de dados pessoais\ncy.validateText('.fs_lg', 'Minha conta');\ncy.validateText('.mb_md > .fs_md', 'Meu perfil');\n//Validando área de dados pessoais\ncy.contains('Dados Pessoais').should('be.visible');\ncy.validateTextContains('Ver dados pessoais');\ncy.validateText('button > :nth-child(3):eq(0)', 'Visualizar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(0)', 'Completo');\n//Validando área email\ncy.contains('E-mail').should('be.visible');\ncy.isVisible('[data-webview-id=\"HidenElement\"] > .d_flex > div > .lh_lg:eq(1)');\ncy.validateText('button > :nth-child(3):eq(1)', 'Editar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(1)', 'Completo');\n//Validando área telefone\ncy.contains('Telefone').should('be.visible');\ncy.isVisible('[data-webview-id=\"HidenElement\"] > .d_flex > div > .lh_lg:eq(2)');\ncy.validateText('button > :nth-child(3):eq(2)', 'Editar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(2)', 'Completo');\n//Validando área endereço\ncy.contains('Endereço').should('be.visible');\ncy.isVisible('[data-webview-id=\"HidenElement\"] > .d_flex > div > .lh_lg:eq(3)');\ncy.validateText('button > :nth-child(3):eq(3)', 'Editar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(3)', 'Completo');\ncy.log('Validando modal alterar senha');\ncy.validateText('.mb_sm > h2:eq(0)', 'Alterar senha');\ncy.validateText('.mb_lg > div > div > label:eq(0)', 'Senha atual');\ncy.validateText('.mb_lg > div > div > label:eq(1)', 'Criar nova senha');\ncy.validateText('.mb_lg > div > div > label:eq(2)', 'Confirme a nova senha');\ncy.get('[type=\"submit\"]:eq(0)').should('have.text', 'Salvar').and('be.disabled');\ncy.log('Validando modal preferencia de comunicação');\ncy.get('[type=\"submit\"]:eq(1)').should('have.text', 'Salvar').and('be.disabled');\ncy.validateText('.mb_sm > h2:eq(1)', 'Preferências de comunicação');\ncy.validateText('.my_md', 'Quero receber notificações relacionadas às minhas solicitações de depósito/saque, apostas grátis, promoções e comunicações de marketing personalizadas da EstrelaBet por:');\n//Opção checkbox\ncy.validateText('[for=\"emailSubscribed\"]', 'E-mail');\ncy.validateText('[for=\"mobileSubscribed\"]', 'SMS');\n//email\ncy.get('[role=\"checkbox\"]:eq(0)').should('have.attr', 'aria-checked', 'true');\ncy.clickMouse('[role=\"checkbox\"]:eq(0)');\ncy.get('[role=\"checkbox\"]:eq(0)').should('have.attr', 'aria-checked', 'false');\ncy.clickMouse('[role=\"checkbox\"]:eq(0)');\ncy.get('[role=\"checkbox\"]:eq(0)').should('have.attr', 'aria-checked', 'true');\n//sms\ncy.get('[role=\"checkbox\"]:eq(1)').should('have.attr', 'aria-checked', 'true');\ncy.clickMouse('[role=\"checkbox\"]:eq(1)');\ncy.get('[role=\"checkbox\"]:eq(1)').should('have.attr', 'aria-checked', 'false');\ncy.clickMouse('[role=\"checkbox\"]:eq(1)');\ncy.get('[role=\"checkbox\"]:eq(1)').should('have.attr', 'aria-checked', 'true');", "err": {}, "uuid": "8efc3938-25fa-4314-8634-d9f9eda037f4", "parentUUID": "4088af73-22aa-4d37-ba6f-3a6309f9011c", "isHook": false, "skipped": false}], "suites": [], "passes": ["8efc3938-25fa-4314-8634-d9f9eda037f4"], "failures": [], "pending": [], "skipped": [], "duration": 25258, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "9d2d8561-0e96-44da-8fb8-ce24509bee3f", "title": "<PERSON>e validar carteira", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "<PERSON>e validar pagina minha carteira", "fullTitle": "Deve validar carteira Deve validar pagina minha carteira", "timedOut": null, "duration": 17343, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.log('Validando área de saldo');\n//Título\ncy.validateText('h1', 'Carte<PERSON>');\n//Validando textos e componentes da area de saldo\ncy.validateText(_locators.default.CARTEIRA.SALDO_TOTAL, 'Saldo total');\ncy.get('p.fs_lg').should('contain.text', 'R$');\n//Saldo livre para saque\ncy.validateText(_locators.default.CARTEIRA.SALDO_LIVRE_PARA_SAQUE, 'Saldo livre para saque');\ncy.get('.d_flex > p.fs_sm:eq(2)').should('contain.text', 'R$');\n//Bônus esportes\ncy.validateText(_locators.default.CARTEIRA.BONUS_ESPORTES, 'Bônus esportes');\ncy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(1)').should('contain.text', 'R$');\n//Bonus cassino\ncy.validateText(_locators.default.CARTEIRA.BONUS_CASSINO, 'Bônus cassino');\ncy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(2)').should('contain.text', 'R$');\n//Saldo restrito\ncy.validateText(_locators.default.CARTEIRA.SALDO_RESTRITO, 'Saldo restrito');\ncy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(3)').should('contain.text', 'R$');\n//botões de ações\ncy.validateText(_locators.default.CARTEIRA.BOTAO_DEPOSITAR, 'Depositar');\ncy.validateText(_locators.default.CARTEIRA.BOTAO_SACAR, 'Sacar');\ncy.log('Validando área das minhas transações');\n//Titulo\ncy.validateText('h2:eq(1)', 'Minhas transações');", "err": {}, "uuid": "31d5ca9e-7f56-43a6-bd28-d8fdfc57f570", "parentUUID": "9d2d8561-0e96-44da-8fb8-ce24509bee3f", "isHook": false, "skipped": false}], "suites": [], "passes": ["31d5ca9e-7f56-43a6-bd28-d8fdfc57f570"], "failures": [], "pending": [], "skipped": [], "duration": 17343, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
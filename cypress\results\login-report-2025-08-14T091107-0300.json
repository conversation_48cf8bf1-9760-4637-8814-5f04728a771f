{"stats": {"suites": 1, "tests": 1, "passes": 0, "pending": 2, "failures": -1, "start": "2025-08-14T12:11:07.618Z", "end": "2025-08-14T12:11:07.693Z", "duration": 75, "testsRegistered": 2, "passPercent": null, "pendingPercent": 100, "other": 1, "hasOther": true, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "79d39ffb-4f8f-464d-b9a5-78075f95a304", "title": "", "fullFile": "cypress\\e2e\\Backend\\login.js", "file": "cypress\\e2e\\Backend\\login.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "de4bec33-be35-4879-9260-403f324c23dd", "title": "<PERSON><PERSON> validar login", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve validar login com sucesso.", "fullTitle": "Deve validar login Deve validar login com sucesso.", "timedOut": null, "duration": 0, "state": "pending", "speed": null, "pass": false, "fail": false, "pending": true, "context": null, "code": "cy.request({\n  method: 'POST',\n  url: 'https://cognito-idp.us-east-1.amazonaws.com/',\n  headers: {\n    'content-type': 'application/x-amz-json-1.1',\n    'x-amz-target': 'AWSCognitoIdentityProviderService.InitiateAuth'\n  },\n  body: {\n    AuthFlow: \"USER_PASSWORD_AUTH\",\n    ClientId: \"39freu20si9ss1tmsggcov5nrq\",\n    AuthParameters: {\n      USERNAME: Cypress.env('user_name'),\n      PASSWORD: Cypress.env('user_password')\n    },\n    \"ClientMetadata\": {}\n  }\n}).then(response => {\n  expect(response.status).to.eq(200);\n  expect(response.body.AuthenticationResult.AccessToken).to.not.empty;\n  expect(response.body.AuthenticationResult.ExpiresIn).to.eq(86400);\n  expect(response.body.AuthenticationResult.IdToken).to.not.empty;\n  expect(response.body.AuthenticationResult.RefreshToken).to.not.empty;\n  expect(response.body.AuthenticationResult.TokenType).to.eq('Bearer');\n  expect(response.body.ChallengeParameters).to.empty;\n});", "err": {}, "uuid": "41b44f5d-24b6-4873-97c2-939cbba84ba8", "parentUUID": "de4bec33-be35-4879-9260-403f324c23dd", "isHook": false, "skipped": false}, {"title": "<PERSON>e validar login incorreto", "fullTitle": "Deve validar login Deve validar login incorreto", "timedOut": null, "duration": 0, "state": "pending", "speed": null, "pass": false, "fail": false, "pending": true, "context": null, "code": "cy.request({\n  method: 'POST',\n  url: 'https://cognito-idp.us-east-1.amazonaws.com/',\n  headers: {\n    'content-type': 'application/x-amz-json-1.1',\n    'x-amz-target': 'AWSCognitoIdentityProviderService.InitiateAuth'\n  },\n  body: {\n    AuthFlow: \"USER_PASSWORD_AUTH\",\n    ClientId: \"3103ticv585k9u5mha4c6ckdq9\",\n    AuthParameters: {\n      USERNAME: Cypress.env('user_name'),\n      PASSWORD: '123321'\n    },\n    \"ClientMetadata\": {}\n  },\n  failOnStatusCode: false\n}).then(response => {\n  expect(response.status).to.eq(400);\n  expect(response.body.__type).to.eq('NotAuthorizedException');\n  expect(response.body.message).to.eq('Incorrect username or password.');\n});", "err": {}, "uuid": "b65233fe-84cf-4347-9750-89fb4f21c3f3", "parentUUID": "de4bec33-be35-4879-9260-403f324c23dd", "isHook": false, "skipped": false}], "suites": [], "passes": [], "failures": [], "pending": ["41b44f5d-24b6-4873-97c2-939cbba84ba8", "b65233fe-84cf-4347-9750-89fb4f21c3f3"], "skipped": [], "duration": 0, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
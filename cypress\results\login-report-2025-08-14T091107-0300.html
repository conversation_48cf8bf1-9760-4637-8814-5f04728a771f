<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:1,&quot;tests&quot;:1,&quot;passes&quot;:0,&quot;pending&quot;:2,&quot;failures&quot;:-1,&quot;start&quot;:&quot;2025-08-14T12:11:07.618Z&quot;,&quot;end&quot;:&quot;2025-08-14T12:11:07.693Z&quot;,&quot;duration&quot;:75,&quot;testsRegistered&quot;:2,&quot;passPercent&quot;:null,&quot;pendingPercent&quot;:100,&quot;other&quot;:1,&quot;hasOther&quot;:true,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;79d39ffb-4f8f-464d-b9a5-78075f95a304&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\Backend\\login.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\Backend\\login.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;de4bec33-be35-4879-9260-403f324c23dd&quot;,&quot;title&quot;:&quot;Deve validar login&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar login com sucesso.&quot;,&quot;fullTitle&quot;:&quot;Deve validar login Deve validar login com sucesso.&quot;,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;code&quot;:&quot;cy.request({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://cognito-idp.us-east-1.amazonaws.com/&#x27;,\n  headers: {\n    &#x27;content-type&#x27;: &#x27;application/x-amz-json-1.1&#x27;,\n    &#x27;x-amz-target&#x27;: &#x27;AWSCognitoIdentityProviderService.InitiateAuth&#x27;\n  },\n  body: {\n    AuthFlow: \&quot;USER_PASSWORD_AUTH\&quot;,\n    ClientId: \&quot;39freu20si9ss1tmsggcov5nrq\&quot;,\n    AuthParameters: {\n      USERNAME: Cypress.env(&#x27;user_name&#x27;),\n      PASSWORD: Cypress.env(&#x27;user_password&#x27;)\n    },\n    \&quot;ClientMetadata\&quot;: {}\n  }\n}).then(response =&gt; {\n  expect(response.status).to.eq(200);\n  expect(response.body.AuthenticationResult.AccessToken).to.not.empty;\n  expect(response.body.AuthenticationResult.ExpiresIn).to.eq(86400);\n  expect(response.body.AuthenticationResult.IdToken).to.not.empty;\n  expect(response.body.AuthenticationResult.RefreshToken).to.not.empty;\n  expect(response.body.AuthenticationResult.TokenType).to.eq(&#x27;Bearer&#x27;);\n  expect(response.body.ChallengeParameters).to.empty;\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;41b44f5d-24b6-4873-97c2-939cbba84ba8&quot;,&quot;parentUUID&quot;:&quot;de4bec33-be35-4879-9260-403f324c23dd&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar login incorreto&quot;,&quot;fullTitle&quot;:&quot;Deve validar login Deve validar login incorreto&quot;,&quot;duration&quot;:0,&quot;state&quot;:&quot;pending&quot;,&quot;pass&quot;:false,&quot;fail&quot;:false,&quot;pending&quot;:true,&quot;code&quot;:&quot;cy.request({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://cognito-idp.us-east-1.amazonaws.com/&#x27;,\n  headers: {\n    &#x27;content-type&#x27;: &#x27;application/x-amz-json-1.1&#x27;,\n    &#x27;x-amz-target&#x27;: &#x27;AWSCognitoIdentityProviderService.InitiateAuth&#x27;\n  },\n  body: {\n    AuthFlow: \&quot;USER_PASSWORD_AUTH\&quot;,\n    ClientId: \&quot;3103ticv585k9u5mha4c6ckdq9\&quot;,\n    AuthParameters: {\n      USERNAME: Cypress.env(&#x27;user_name&#x27;),\n      PASSWORD: &#x27;123321&#x27;\n    },\n    \&quot;ClientMetadata\&quot;: {}\n  },\n  failOnStatusCode: false\n}).then(response =&gt; {\n  expect(response.status).to.eq(400);\n  expect(response.body.__type).to.eq(&#x27;NotAuthorizedException&#x27;);\n  expect(response.body.message).to.eq(&#x27;Incorrect username or password.&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;b65233fe-84cf-4347-9750-89fb4f21c3f3&quot;,&quot;parentUUID&quot;:&quot;de4bec33-be35-4879-9260-403f324c23dd&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[&quot;41b44f5d-24b6-4873-97c2-939cbba84ba8&quot;,&quot;b65233fe-84cf-4347-9750-89fb4f21c3f3&quot;],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;overwrite&quot;:true,&quot;html&quot;:true,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;cypress\\results\\assets&quot;,&quot;jsonFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\login-report-2025-08-14T091107-0300.json&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\login-report-2025-08-14T091107-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>
<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:2,&quot;tests&quot;:2,&quot;passes&quot;:2,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-08-13T23:53:36.063Z&quot;,&quot;end&quot;:&quot;2025-08-13T23:54:42.477Z&quot;,&quot;duration&quot;:66414,&quot;testsRegistered&quot;:2,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;00f944f6-bf18-49cf-88e0-7cddf3c6f770&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\minhaConta.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;ead25040-2465-4efe-9a91-7426e42bb3a0&quot;,&quot;title&quot;:&quot;Deve validar opções da conta&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Validando pagina da minha conta&quot;,&quot;fullTitle&quot;:&quot;Deve validar opções da conta Validando pagina da minha conta&quot;,&quot;duration&quot;:47052,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;//Validando avatar\ncy.isVisible(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;);\n//Abrindo opções do usuário\ncy.clickMouse(&#x27;.nebulosa-header__desktopButtonWrapper &gt; .nebulosa-avatar__avatarContainer &gt; .nebulosa-avatar__root &gt; .nebulosa-avatar__fallback&#x27;);\n//Validando opção Minha conta e acessando\ncy.validateText(&#x27;:nth-child(4) &gt; :nth-child(2) &gt; .fw_regular&#x27;, &#x27;Minha conta&#x27;);\ncy.clickMouse(&#x27;:nth-child(4) &gt; :nth-child(2) &gt; .fw_regular&#x27;);\n//validando opções de acesso do usuário\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(0)&#x27;).should(&#x27;have.text&#x27;, &#x27;Conta&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(1)&#x27;).should(&#x27;have.text&#x27;, &#x27;Carteira&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(2)&#x27;).should(&#x27;have.text&#x27;, &#x27;Bônus&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(3)&#x27;).should(&#x27;have.text&#x27;, &#x27;Apostas&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(4)&#x27;).should(&#x27;have.text&#x27;, &#x27;Limites&#x27;).and(&#x27;be.visible&#x27;);\ncy.get(&#x27;[role=\&quot;menuitem\&quot;] &gt; label:eq(5)&#x27;).should(&#x27;have.text&#x27;, &#x27;Segurança&#x27;).and(&#x27;be.visible&#x27;);\ncy.log(&#x27;Validando modal Minha conta&#x27;);\n//Titulo e subtítulo da área de dados pessoais\ncy.validateText(&#x27;.fs_lg&#x27;, &#x27;Minha conta&#x27;);\ncy.validateText(&#x27;.mb_md &gt; .fs_md&#x27;, &#x27;Meu perfil&#x27;);\n//Validando área de dados pessoais\ncy.contains(&#x27;Dados Pessoais&#x27;).should(&#x27;be.visible&#x27;);\ncy.validateTextContains(&#x27;Ver dados pessoais&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(0)&#x27;, &#x27;Visualizar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(0)&#x27;, &#x27;Completo&#x27;);\n//Validando área email\ncy.contains(&#x27;E-mail&#x27;).should(&#x27;be.visible&#x27;);\ncy.isVisible(&#x27;[data-webview-id=\&quot;HidenElement\&quot;] &gt; .d_flex &gt; div &gt; .lh_lg:eq(1)&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(1)&#x27;, &#x27;Editar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(1)&#x27;, &#x27;Completo&#x27;);\n//Validando área telefone\ncy.contains(&#x27;Telefone&#x27;).should(&#x27;be.visible&#x27;);\ncy.isVisible(&#x27;[data-webview-id=\&quot;HidenElement\&quot;] &gt; .d_flex &gt; div &gt; .lh_lg:eq(2)&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(2)&#x27;, &#x27;Editar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(2)&#x27;, &#x27;Completo&#x27;);\n//Validando área endereço\ncy.contains(&#x27;Endereço&#x27;).should(&#x27;be.visible&#x27;);\ncy.isVisible(&#x27;[data-webview-id=\&quot;HidenElement\&quot;] &gt; .d_flex &gt; div &gt; .lh_lg:eq(3)&#x27;);\ncy.validateText(&#x27;button &gt; :nth-child(3):eq(3)&#x27;, &#x27;Editar&#x27;);\ncy.validateText(&#x27;.nebulosa-badge__root--type_success &gt; label:eq(3)&#x27;, &#x27;Completo&#x27;);\ncy.log(&#x27;Validando modal alterar senha&#x27;);\ncy.validateText(&#x27;.mb_sm &gt; h2:eq(0)&#x27;, &#x27;Alterar senha&#x27;);\ncy.validateText(&#x27;.mb_lg &gt; div &gt; div &gt; label:eq(0)&#x27;, &#x27;Senha atual&#x27;);\ncy.validateText(&#x27;.mb_lg &gt; div &gt; div &gt; label:eq(1)&#x27;, &#x27;Criar nova senha&#x27;);\ncy.validateText(&#x27;.mb_lg &gt; div &gt; div &gt; label:eq(2)&#x27;, &#x27;Confirme a nova senha&#x27;);\ncy.get(&#x27;[type=\&quot;submit\&quot;]:eq(0)&#x27;).should(&#x27;have.text&#x27;, &#x27;Salvar&#x27;).and(&#x27;be.disabled&#x27;);\ncy.log(&#x27;Validando modal preferencia de comunicação&#x27;);\ncy.get(&#x27;[type=\&quot;submit\&quot;]:eq(1)&#x27;).should(&#x27;have.text&#x27;, &#x27;Salvar&#x27;).and(&#x27;be.disabled&#x27;);\ncy.validateText(&#x27;.mb_sm &gt; h2:eq(1)&#x27;, &#x27;Preferências de comunicação&#x27;);\ncy.validateText(&#x27;.my_md&#x27;, &#x27;Quero receber notificações relacionadas às minhas solicitações de depósito/saque, apostas grátis, promoções e comunicações de marketing personalizadas da EstrelaBet por:&#x27;);\n//Opção checkbox\ncy.validateText(&#x27;[for=\&quot;emailSubscribed\&quot;]&#x27;, &#x27;E-mail&#x27;);\ncy.validateText(&#x27;[for=\&quot;mobileSubscribed\&quot;]&#x27;, &#x27;SMS&#x27;);\n//email\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;false&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(0)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);\n//sms\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;false&#x27;);\ncy.clickMouse(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;);\ncy.get(&#x27;[role=\&quot;checkbox\&quot;]:eq(1)&#x27;).should(&#x27;have.attr&#x27;, &#x27;aria-checked&#x27;, &#x27;true&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;777a078c-b161-4ad7-8d8d-ed9df4e0bcc4&quot;,&quot;parentUUID&quot;:&quot;ead25040-2465-4efe-9a91-7426e42bb3a0&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;777a078c-b161-4ad7-8d8d-ed9df4e0bcc4&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:47052,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000},{&quot;uuid&quot;:&quot;262e87fd-f5da-4f51-b66c-337786bc8414&quot;,&quot;title&quot;:&quot;Deve validar carteira&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar pagina minha carteira&quot;,&quot;fullTitle&quot;:&quot;Deve validar carteira Deve validar pagina minha carteira&quot;,&quot;duration&quot;:11681,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.log(&#x27;Validando área de saldo&#x27;);\n//Título\ncy.validateText(&#x27;h1&#x27;, &#x27;Carteira&#x27;);\n//Validando textos e componentes da area de saldo\ncy.validateText(_locators.default.CARTEIRA.SALDO_TOTAL, &#x27;Saldo total&#x27;);\ncy.get(&#x27;p.fs_lg&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Saldo livre para saque\ncy.validateText(_locators.default.CARTEIRA.SALDO_LIVRE_PARA_SAQUE, &#x27;Saldo livre para saque&#x27;);\ncy.get(&#x27;.d_flex &gt; p.fs_sm:eq(2)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Bônus esportes\ncy.validateText(_locators.default.CARTEIRA.BONUS_ESPORTES, &#x27;Bônus esportes&#x27;);\ncy.get(&#x27;div.flex-d_row &gt; .flex-d_column &gt; p.fs_xs:eq(1)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Bonus cassino\ncy.validateText(_locators.default.CARTEIRA.BONUS_CASSINO, &#x27;Bônus cassino&#x27;);\ncy.get(&#x27;div.flex-d_row &gt; .flex-d_column &gt; p.fs_xs:eq(2)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//Saldo restrito\ncy.validateText(_locators.default.CARTEIRA.SALDO_RESTRITO, &#x27;Saldo restrito&#x27;);\ncy.get(&#x27;div.flex-d_row &gt; .flex-d_column &gt; p.fs_xs:eq(3)&#x27;).should(&#x27;contain.text&#x27;, &#x27;R$&#x27;);\n//botões de ações\ncy.validateText(_locators.default.CARTEIRA.BOTAO_DEPOSITAR, &#x27;Depositar&#x27;);\ncy.validateText(_locators.default.CARTEIRA.BOTAO_SACAR, &#x27;Sacar&#x27;);\ncy.log(&#x27;Validando área das minhas transações&#x27;);\n//Titulo\ncy.validateText(&#x27;h2:eq(1)&#x27;, &#x27;Minhas transações&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;34cd178e-315b-4973-a60c-59d6c8350f1a&quot;,&quot;parentUUID&quot;:&quot;262e87fd-f5da-4f51-b66c-337786bc8414&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;34cd178e-315b-4973-a60c-59d6c8350f1a&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:11681,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;overwrite&quot;:true,&quot;html&quot;:true,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;cypress\\results\\assets&quot;,&quot;jsonFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\minhaConta-report-2025-08-13T205442-0300.json&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\minhaConta-report-2025-08-13T205442-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>
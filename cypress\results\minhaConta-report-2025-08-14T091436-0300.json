{"stats": {"suites": 2, "tests": 2, "passes": 2, "pending": 0, "failures": 0, "start": "2025-08-14T12:14:04.672Z", "end": "2025-08-14T12:14:36.502Z", "duration": 31830, "testsRegistered": 2, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "2bbfcff6-9e21-4cb4-8820-b213e914b3d0", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\minhaConta.js", "file": "cypress\\e2e\\FrontEnd\\minhaConta.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "11359053-758a-4490-97fb-a31da8de8dc8", "title": "Deve validar opções da conta", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Validando pagina da minha conta", "fullTitle": "Deve validar opções da conta Validando pagina da minha conta", "timedOut": null, "duration": 16171, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Validando avatar\ncy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Abrindo opções do usuário\ncy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Validando opção Minha conta e acessando\ncy.validateText(':nth-child(4) > :nth-child(2) > .fw_regular', 'Minha conta');\ncy.clickMouse(':nth-child(4) > :nth-child(2) > .fw_regular');\n//validando opções de acesso do usuário\ncy.get('[role=\"menuitem\"] > label:eq(0)').should('have.text', 'Conta').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(1)').should('have.text', '<PERSON><PERSON><PERSON>').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(2)').should('have.text', 'Bônus').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(3)').should('have.text', 'Apostas').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(4)').should('have.text', 'Limites').and('be.visible');\ncy.get('[role=\"menuitem\"] > label:eq(5)').should('have.text', 'Segurança').and('be.visible');\ncy.log('Validando modal Minha conta');\n//Titulo e subtítulo da área de dados pessoais\ncy.validateText('.fs_lg', 'Minha conta');\ncy.validateText('.mb_md > .fs_md', 'Meu perfil');\n//Validando área de dados pessoais\ncy.contains('Dados Pessoais').should('be.visible');\ncy.validateTextContains('Ver dados pessoais');\ncy.validateText('button > :nth-child(3):eq(0)', 'Visualizar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(0)', 'Completo');\n//Validando área email\ncy.contains('E-mail').should('be.visible');\ncy.isVisible('[data-webview-id=\"HidenElement\"] > .d_flex > div > .lh_lg:eq(1)');\ncy.validateText('button > :nth-child(3):eq(1)', 'Editar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(1)', 'Completo');\n//Validando área telefone\ncy.contains('Telefone').should('be.visible');\ncy.isVisible('[data-webview-id=\"HidenElement\"] > .d_flex > div > .lh_lg:eq(2)');\ncy.validateText('button > :nth-child(3):eq(2)', 'Editar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(2)', 'Completo');\n//Validando área endereço\ncy.contains('Endereço').should('be.visible');\ncy.isVisible('[data-webview-id=\"HidenElement\"] > .d_flex > div > .lh_lg:eq(3)');\ncy.validateText('button > :nth-child(3):eq(3)', 'Editar');\ncy.validateText('.nebulosa-badge__root--type_success > label:eq(3)', 'Completo');\ncy.log('Validando modal alterar senha');\ncy.validateText('.mb_sm > h2:eq(0)', 'Alterar senha');\ncy.validateText('.mb_lg > div > div > label:eq(0)', 'Senha atual');\ncy.validateText('.mb_lg > div > div > label:eq(1)', 'Criar nova senha');\ncy.validateText('.mb_lg > div > div > label:eq(2)', 'Confirme a nova senha');\ncy.get('[type=\"submit\"]:eq(0)').should('have.text', 'Salvar').and('be.disabled');\ncy.log('Validando modal preferencia de comunicação');\ncy.get('[type=\"submit\"]:eq(1)').should('have.text', 'Salvar').and('be.disabled');\ncy.validateText('.mb_sm > h2:eq(1)', 'Preferências de comunicação');\ncy.validateText('.my_md', 'Quero receber notificações relacionadas às minhas solicitações de depósito/saque, apostas grátis, promoções e comunicações de marketing personalizadas da EstrelaBet por:');\n//Opção checkbox\ncy.validateText('[for=\"emailSubscribed\"]', 'E-mail');\ncy.validateText('[for=\"mobileSubscribed\"]', 'SMS');\n//email\ncy.get('[role=\"checkbox\"]:eq(0)').should('have.attr', 'aria-checked', 'true');\ncy.clickMouse('[role=\"checkbox\"]:eq(0)');\ncy.get('[role=\"checkbox\"]:eq(0)').should('have.attr', 'aria-checked', 'false');\ncy.clickMouse('[role=\"checkbox\"]:eq(0)');\ncy.get('[role=\"checkbox\"]:eq(0)').should('have.attr', 'aria-checked', 'true');\n//sms\ncy.get('[role=\"checkbox\"]:eq(1)').should('have.attr', 'aria-checked', 'true');\ncy.clickMouse('[role=\"checkbox\"]:eq(1)');\ncy.get('[role=\"checkbox\"]:eq(1)').should('have.attr', 'aria-checked', 'false');\ncy.clickMouse('[role=\"checkbox\"]:eq(1)');\ncy.get('[role=\"checkbox\"]:eq(1)').should('have.attr', 'aria-checked', 'true');", "err": {}, "uuid": "21d3b396-0ff2-450e-962b-bdef0c4b67d6", "parentUUID": "11359053-758a-4490-97fb-a31da8de8dc8", "isHook": false, "skipped": false}], "suites": [], "passes": ["21d3b396-0ff2-450e-962b-bdef0c4b67d6"], "failures": [], "pending": [], "skipped": [], "duration": 16171, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "b8e88298-8244-464a-921a-54298254afe4", "title": "<PERSON>e validar carteira", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "<PERSON>e validar pagina minha carteira", "fullTitle": "Deve validar carteira Deve validar pagina minha carteira", "timedOut": null, "duration": 5570, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.log('Validando área de saldo');\n//Título\ncy.validateText('h1', 'Carte<PERSON>');\n//Validando textos e componentes da area de saldo\ncy.validateText(_locators.default.CARTEIRA.SALDO_TOTAL, 'Saldo total');\ncy.get('p.fs_lg').should('contain.text', 'R$');\n//Saldo livre para saque\ncy.validateText(_locators.default.CARTEIRA.SALDO_LIVRE_PARA_SAQUE, 'Saldo livre para saque');\ncy.get('.d_flex > p.fs_sm:eq(2)').should('contain.text', 'R$');\n//Bônus esportes\ncy.validateText(_locators.default.CARTEIRA.BONUS_ESPORTES, 'Bônus esportes');\ncy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(1)').should('contain.text', 'R$');\n//Bonus cassino\ncy.validateText(_locators.default.CARTEIRA.BONUS_CASSINO, 'Bônus cassino');\ncy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(2)').should('contain.text', 'R$');\n//Saldo restrito\ncy.validateText(_locators.default.CARTEIRA.SALDO_RESTRITO, 'Saldo restrito');\ncy.get('div.flex-d_row > .flex-d_column > p.fs_xs:eq(3)').should('contain.text', 'R$');\n//botões de ações\ncy.validateText(_locators.default.CARTEIRA.BOTAO_DEPOSITAR, 'Depositar');\ncy.validateText(_locators.default.CARTEIRA.BOTAO_SACAR, 'Sacar');\ncy.log('Validando área das minhas transações');\n//Titulo\ncy.validateText('h2:eq(1)', 'Minhas transações');\ncy.contains('button > span', 'Ver minhas transações').should('be.visible').click();\n//Validando título do periodo de e até\ncy.validateText('.d_flex > label:eq(1)', 'Período de');\ncy.validateText('.d_flex > label:eq(2)', 'Período até');\n//Tipo de transação\ncy.validateText('.nebulosa-select__Wrapper > label', 'Tipo de transação');\ncy.clickMouse('[role=\"combobox\"]:eq(0)');\nconst tipoTransacao = ['Depósito', 'Saque cancelado', 'Saque', 'Ajuste de saldo positivo', 'Ajuste de saldo negativo', 'Todos/Todas'];\ncy.get('[role=\"option\"] > span').should('have.length', 6).then($items => {\n  const firstSix = $items.slice(0, 11);\n  firstSix.each((index, item) => {\n    expect(Cypress.$(item).text()).to.contain(tipoTransacao[index]);\n  });\n});\ncy.get('body').type('{esc}'); //Fechando o select\n//Botão filtrar\ncy.validateText('[type=\"submit\"]', 'Filtrar');\n//Validando título das colunas da tabela\ncy.validateText('th:eq(0)', 'Status');\ncy.validateText('th:eq(1)', 'Data e hora');\ncy.validateText('th:eq(2)', 'Detalhe da transação');\ncy.validateText('th:eq(3)', 'Transação');\ncy.validateText('th:eq(4)', 'Saldo anterior');\ncy.validateText('th:eq(5)', 'Valor da transação');\ncy.validateText('th:eq(6)', 'Saldo final');\ncy.validateText('td > p', 'Nenhuma transação disponível');\n//Botão solicitar histórico\ncy.validateText('[lefticon=\"utility-square-list-solid\"]', 'Solicitar histórico de 36 meses');\ncy.log('Validando modal dos dados bancários');\n//Título\ncy.validateText('section > h3', 'Dados bancários');\ncy.validateText('section > p', 'Certifique-se de que as chaves Pix e conta bancária esteja vinculada ao seu CPF.');\n//Informações da conta\ncy.validateText('article > .fw_bold', 'Chave(s) Pix');\ncy.isVisible('li > span:eq(0)');\ncy.isVisible('li > span:eq(1)');\ncy.isVisible('li > span:eq(2)');\ncy.validateText('.gap_xs > .gap_md > .nebulosa-button__root > .nebulosa-button__buttonLabel', 'Alterar dados');\ncy.log('Validando área de informe de rendimento');", "err": {}, "uuid": "0990e629-4338-4ede-97f2-ad757b145ccb", "parentUUID": "b8e88298-8244-464a-921a-54298254afe4", "isHook": false, "skipped": false}], "suites": [], "passes": ["0990e629-4338-4ede-97f2-ad757b145ccb"], "failures": [], "pending": [], "skipped": [], "duration": 5570, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
import loc from '../support/locators'
import { faker } from '@faker-js/faker/locale/pt_BR';
import { Utility } from "../support/utility"
import messages from '../support/validationMessages'
import { blockTrackers } from '../support/blockTrackers';

const dayjs = require('dayjs');
const localizedFormat = require('dayjs/plugin/localizedFormat');
dayjs.extend(localizedFormat);

const url = new Utility().getBaseUrl();

// ========== BASIC INTERACTION COMMANDS ==========

Cypress.Commands.add('clickByContains', (selector, text) => {
    cy.get(selector).contains(text).then($el => {
        if ($el.is(':visible')) {
            cy.wrap($el).scrollIntoView().click();
        } else {
            cy.wrap($el).click({ force: true });
        }
    });
});

Cypress.Commands.add('clickMouse', (locator) => {
    cy.get(locator, { timeout: 10000 })
        .scrollIntoView()
        .should('be.visible')
        .click()
});

Cypress.Commands.add('typeText', (locator, text) => {
    cy.get(locator).type(text).should('have.value', text)
});

Cypress.Commands.add('isVisible', (locator) => {
    cy.get(locator, { timeout: 10000 }).should('be.visible').and('exist')
});

// ========== VALIDATION COMMANDS ==========

Cypress.Commands.add('validateText', (locator, text) => {
    cy.get(locator).should('have.text', text)
        .and('be.visible')
})

Cypress.Commands.add('validateTextContains', (text) => {
    cy.contains(text)
        .should('have.text', text)
        .and('exist')
        .and('be.visible');
});

Cypress.Commands.add('validatePlaceholder', (locator, text) => {
    cy.get(locator)
        .invoke('attr', 'placeholder')
        .should('to.eq', text)
        .then(() => {
            cy.get(locator)
                .should('be.visible')
                .and('exist');
        });
});

Cypress.Commands.add('navigateToRegistration', (method = 'header') => {
    cy.visit(new Utility().getBaseUrl())

    if (method === 'header') {
        cy.log('Acessando cadastro via botão do header')
        cy.contains('button', 'Cadastrar')
        cy.get(loc.CADASTRO.BOTAO_CADASTRAR_HEADER).click()
    } else if (method === 'home') {
        cy.log('Acessando cadastro via botão da home')
        cy.isVisible('button', 'Cadastre-se')
        cy.get(loc.CADASTRO.BOTAO_CADASTRAR_HOME)
            .contains('Cadastre-se')
            .should('be.visible')
            .click()
    }
});

Cypress.Commands.add('validateRegistrationPageElements', () => {
    cy.log('Validando elementos da tela de cadastro')

    // Elementos do cabeçalho
    cy.isVisible('button', 'Suporte')
    cy.isVisible(loc.CADASTRO.LOGO_CLIENTE)
    cy.validateText(loc.CADASTRO.TITULO_PAGINA, 'Vamos começar seu cadastro')

    // Campos de entrada
    cy.isVisible(loc.CADASTRO.CAMPO_CPF)
    cy.validatePlaceholder(loc.CADASTRO.CAMPO_CPF, 'CPF *')
    cy.contains('button', 'Verificar')
        .should('be.visible')
        .and('have.attr', 'type', 'button')

    cy.isVisible(loc.CADASTRO.CAMPO_EMAIL)
    cy.validatePlaceholder(loc.CADASTRO.CAMPO_EMAIL, 'E-mail *')

    cy.isVisible(loc.CADASTRO.CAMPO_CODIGO_AREA)
    cy.validatePlaceholder(loc.CADASTRO.CAMPO_CODIGO_AREA, 'Ex: +55')

    cy.isVisible(loc.CADASTRO.CAMPO_TELEFONE)
    cy.validatePlaceholder(loc.CADASTRO.CAMPO_TELEFONE, 'Celular *')

    cy.isVisible(loc.CADASTRO.CAMPO_SENHA)
    cy.validatePlaceholder(loc.CADASTRO.CAMPO_SENHA, 'Senha *')

    // Checkbox de termos e condições
    cy.get(loc.CADASTRO.CHECKBOX_TERMOS)
        .should('have.attr', 'role', 'checkbox')
        .and('have.attr', 'aria-checked', 'false')
        .and('be.visible');

    // Texto de confirmação
    cy.isVisible('p', 'Confirmo que tenho mais de 18 anos e aceito')

    // Links de termos e política
    cy.get(loc.CADASTRO.LINK_TERMOS)
        .should('be.visible')
        .and('contain', 'Termos e condições')
    cy.get(loc.CADASTRO.LINK_POLITICA)
        .should('be.visible')
        .and('contain', 'Política de privacidade')

    // Botão de conclusão
    cy.isVisible('button', 'Concluir cadastro')

    // Link para login
    cy.isVisible('p', 'Já possui uma conta?')
    cy.get(loc.CADASTRO.LINK_ENTRAR)
        .should('be.visible')
        .and('contain', 'Entrar')
});

Cypress.Commands.add('validateCurrencyValue', (selector, expectedValue) => {
    cy.get(selector).should(($el) => {
        const text = $el.text().replace(/\s/g, ''); // remove spaces
        const regex = /^R\$\d{1,3}(?:\.\d{3})*,\d{2}$/;
        expect(text).to.match(regex);
        if (expectedValue !== undefined) {
            expect(text).to.equal(expectedValue);
        }
    });
});

Cypress.Commands.add('validateResponseTime', (response, maxTime = 1000) => {
    expect(response).to.have.property('duration')
    expect(response.duration).to.be.lessThan(
        maxTime,
        `Response time exceeded limit: ${response.duration}ms > ${maxTime}ms`
    )

    Cypress.log({
        name: 'validateResponseTime',
        displayName: '⏱️ Response Time',
        message: `Duration: ${response.duration}ms`,
        consoleProps: () => {
            return {
                'Request Duration (ms)': response.duration,
                'Max Accepted Time (ms)': maxTime
            }
        }
    })
});

// ========== AUTHENTICATION COMMANDS ==========

Cypress.Commands.add("login", (username, password) => {
    // Bloqueia terceiros e aplica otimizações de performance
    blockTrackers();
    cy.intercept("POST", "https://hml.estrelabet.bet.br/next/pb/api/login").as("login");
    cy.visit(url)
    cy.get('.nebulosa-header__desktopButtonWrapper > :nth-child(2) > .nebulosa-button__root',
        { timeout: 10000 }).click()
    cy.get(loc.LOGIN.EMAIL_CAMPO)
        .type(username)
        .should('have.value', username)
    cy.get(loc.LOGIN.SENHA_CAMPO)
        .type(password, { log: false })

    cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
    cy.wait('@login').its('response.statusCode').should('eq', 200)
    cy.get('button.nebulosa-button__root')
        .contains('Aceitar todos os cookies')
        .then($btn => {
            if ($btn.length > 0) {
                cy.wrap($btn).click();
            }
            // Se não existir, não faz nada e o teste continua normalmente
        });
});

Cypress.Commands.add("loginToAuth0", (username, password) => {
    const log = Cypress.log({
        displayName: "AUTH0 LOGIN",
        message: [`🔐 Authenticating | ${username}`],
        autoEnd: false,
    });
    cy.intercept("POST", "https://hml.estrelabet.bet.br/next/pb/api/login").as("loginUser");

    cy.session(
        `auth0-${username}`,
        () => {
            cy.visit(url)
            cy.clickMouse('.nebulosa-header__desktopButtonWrapper > :nth-child(2) > .nebulosa-button__root')
            cy.get(loc.LOGIN.EMAIL_CAMPO)
                .type(username)
                .should('have.value', username)
            cy.get(loc.LOGIN.SENHA_CAMPO)
                .type(password, { log: false })

            cy.clickMouse(loc.LOGIN.BOTAO_ENTRAR)
            cy.wait('@loginUser').its('response.statusCode').should('eq', 200)
            cy.get('button.nebulosa-button__root')
                .contains('Aceitar todos os cookies')
                .then($btn => {
                    if ($btn.length > 0) {
                        cy.wrap($btn).click();
                    }
                    // Se não existir, não faz nada e o teste continua normalmente
                });
        }
    );
    cy.visit(url);
    log.end()
});

// ========== DATA GENERATION COMMANDS ==========

Cypress.Commands.add('generatePhoneNumber', () => {
    const ddd = Math.floor(Math.random() * 100).toString().padStart(2, '0');
    const part1 = Math.floor(Math.random() * 9000) + 1000;
    const part2 = Math.floor(Math.random() * 10000).toString().padStart(4, '0');
    const phoneNumber = `${ddd}9${part1}${part2}`;
    cy.wrap(phoneNumber).as('phoneNumber');
});

Cypress.Commands.add('generateCPF', () => {
    const rnd = (n) => Math.round(Math.random() * n);
    const mod = (base, div) => Math.round(base - Math.floor(base / div) * div);
    const n = Array(9).fill('').map(() => rnd(9));

    let d1 = n.reduce((total, number, index) => total + number * (10 - index), 0);
    d1 = 11 - mod(d1, 11);
    if (d1 >= 10) d1 = 0;

    let d2 = d1 * 2 + n.reduce((total, number, index) => total + number * (11 - index), 0);
    d2 = 11 - mod(d2, 11);
    if (d2 >= 10) d2 = 0;

    const cpf = `${n.join('')}${d1}${d2}`;
    return cpf;
});

Cypress.Commands.add('generateFullName', () => {
    const firstName = faker.person.firstName();
    const lastName = faker.person.lastName();
    const fullName = `${firstName} ${lastName}`;
    cy.wrap(fullName).as('fullName');
});

Cypress.Commands.add('generatePastDate', () => {
    const daysAgo = Math.floor(Math.random() * 365) + 1;
    const pastDate = dayjs().subtract(daysAgo, 'day');
    const formattedDate = pastDate.format('DDMMYYYY');
    return formattedDate;
});

// ========== UTILITY COMMANDS ==========

Cypress.Commands.add('selectRandomElement', { prevSubject: 'element' }, (subject, size = 1) => {
    cy.wrap(subject).then(elementList => {
        elementList = (elementList.jquery) ? elementList.get() : elementList;
        elementList = Cypress._.sampleSize(elementList, size);
        elementList = (elementList.length > 1) ? elementList : elementList[0];
        cy.wrap(elementList);
    });
});

Cypress.Commands.add('switchToIframe', (iframe) => {
    return cy
        .get(iframe)
        .its('0.contentDocument.body')
        .should('not.be.empty')
        .then(cy.wrap)
});

Cypress.Commands.add('selectRandomPageSize', (alias) => {
    const possibleValues = [10, 50, 100, 250, 500];
    const randomValue = Cypress._.sample(possibleValues);

    cy.get('[role="combobox"]').click({ force: true });
    cy.contains(randomValue).scrollIntoView().click({ force: true });

    cy.wait(alias).then((intercept) => {
        expect(intercept.response.statusCode).to.eq(200);
        expect(intercept.request.url).to.include(`page=1&s=&size=${randomValue}`);
    })

    cy.contains('[role="combobox"] > [style="pointer-events: none;"]', randomValue)

    cy.get('.flew-row > .font-normal').invoke('text').then(text => {
        const match = text.match(/(\d+)/);
        if (match && match.length > 0) {
            const totalRecords = parseInt(match[0]);
            const totalPages = Math.ceil(totalRecords / randomValue);

            if (totalPages <= 10) {
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').should('have.length', totalPages);
            } else {
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').should('have.length', 7);
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').contains('...').should('be.visible');
                cy.get('[aria-label="Pagination"] > [aria-current="page"]').contains(totalPages.toString()).should('be.visible');
            }
        }
    });
});

// ========== API COMMANDS ==========

Cypress.Commands.add('getToken', () => {
    cy.api({
        method: 'POST',
        url: 'https://cognito-idp.us-east-1.amazonaws.com/',
        headers: {
            'content-type': 'application/x-amz-json-1.1',
            'x-amz-target': 'AWSCognitoIdentityProviderService.InitiateAuth'
        },
        body: {
            AuthFlow: "USER_PASSWORD_AUTH",
            ClientId: "4dfbu25e3cp1thk7qm6nmstleq",
            AuthParameters: {
                USERNAME: Cypress.env('user_name'),
                PASSWORD: Cypress.env('user_password')
            },
            "ClientMetadata": {}
        },
    }).its('body').should('include.keys', ['AuthenticationResult'])
        .then(responseBody => {
            const authorizationToken = responseBody.AuthenticationResult.AccessToken;
            const identityToken = responseBody.AuthenticationResult.IdToken;
            Cypress.env('authorization', authorizationToken);
            Cypress.env('identity', identityToken);

            return { authorizationToken, identityToken };
        });
});

Cypress.Commands.add('getAuthToken', () => {
    cy.api({
        method: 'GET',
        url: 'https://api.alfa.playprime.com.br/backoffice/api/v2/users/me',
        headers: {
            Authorization: `Bearer ${Cypress.env('authorization')}`,
            Identity: Cypress.env('identity'),
        },
    }).then(response => {
        const authtoken = response.body.token;
        Cypress.env('authtoken', authtoken);

        return { authtoken };
    });
});

Cypress.Commands.add('stubClipboard', () => {
    cy.window().then((win) => {
        if (!win.navigator.clipboard) {
            win.navigator.clipboard = { writeText: () => Promise.resolve() };
        } else {
            cy.stub(win.navigator.clipboard, 'writeText').as('clipboardWrite').resolves();
        }
    });
});
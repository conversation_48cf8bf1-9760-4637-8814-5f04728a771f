{"uuid": "c41d8324-f83d-4a6e-a27d-9246df3a10e1", "historyId": "2d49dc3db4ea7738ff70a8ccf4e2bb85", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518970471, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/validate/email POST", "stop": 1755518970471}], "attachments": [], "parameters": [], "start": 1755518970280, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 19 more characters, body: { email: '<EMAIL>' } }\")", "stop": 1755518970471}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518970477, "name": "assert expected **false** to be false", "stop": 1755518970477}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 200}, {"name": "expected", "value": 200}], "start": 1755518970477, "name": "assert expected **200** to equal **200**", "stop": 1755518970477}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518970248, "name": "Deve retornar que o e-mail já está cadastrado", "fullName": "Deve retornar que o e-mail já está cadastrado", "stop": 1755518970481}
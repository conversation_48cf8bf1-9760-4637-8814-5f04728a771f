{"stats": {"suites": 1, "tests": 11, "passes": 11, "pending": 0, "failures": 0, "start": "2025-08-18T12:09:28.346Z", "end": "2025-08-18T12:09:31.456Z", "duration": 3110, "testsRegistered": 11, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "5e43578c-77e6-4f0b-9ce9-7fe4e8bfd223", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\Cadastro.js", "file": "cypress\\e2e\\FrontEnd\\Cadastro.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "18af09e4-8865-4add-b74d-9070296de084", "title": "Cadastro", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve validar elementos no cadastro via botão do header", "fullTitle": "Cadastro Deve validar elementos no cadastro via botão do header", "timedOut": null, "duration": 242, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// Anotações do Allure Report\ncy.allure().epic('Frontend').feature('Cadastro de Usuário').story('Validação de Elementos da Tela').severity('critical').tag('smoke', 'ui', 'cadastro').description('Valida se todos os elementos da tela de cadastro estão visíveis e funcionais quando acessada via botão do header').issue('JIRA-123').tms('TC-001');\n(0, _blockTrackers.blockTrackers)();\ncy.allure().step('Navegar para a tela de cadastro via header', () => {\n  cy.navigateToRegistration('header');\n});\ncy.allure().step('Validar todos os elementos da tela de cadastro', () => {\n  cy.validateRegistrationPageElements();\n});", "err": {}, "uuid": "e972b9dc-8ada-43f0-87a2-919c18e0c007", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve validar elementos no cadastro via botão da home", "fullTitle": "Cadastro Deve validar elementos no cadastro via botão da home", "timedOut": null, "duration": 172, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// Anotações do Allure Report\ncy.allure().epic('Frontend').feature('Cadastro de Usuário').story('Validação de Elementos da Tela').severity('normal').tag('ui', 'cadastro', 'home').description('Valida se todos os elementos da tela de cadastro estão visíveis quando acessada via botão da página inicial');\n(0, _blockTrackers.blockTrackers)();\ncy.allure().step('Navegar para a tela de cadastro via botão da home', () => {\n  cy.navigateToRegistration('home');\n});\ncy.allure().step('Validar todos os elementos da tela de cadastro', () => {\n  cy.validateRegistrationPageElements();\n});", "err": {}, "uuid": "cb99b027-2bb7-4bd2-97a5-ce51b80d6683", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 400 quando CPF já está cadastrado", "fullTitle": "Cadastro Deve retornar 400 quando CPF já está cadastrado", "timedOut": null, "duration": 47, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// Anotações do Allure Report\ncy.allure().epic('Backend').feature('Validação de CPF').story('CPF Duplicado').severity('critical').tag('api', 'validation', 'cpf').description('Valida que a API retorna erro 400 quando um CPF já cadastrado é enviado').issue('JIRA-456').tms('TC-002');\ncy.intercept('POST', '**/validate/cpf').as('validateCpf');\nconst requestBody = {\n  cpf: \"11983538647\"\n};\nconst url = 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/cpf';\ncy.allure().step('Enviar requisição para validar CPF já cadastrado', () => {\n  cy.api({\n    method: 'POST',\n    url: url,\n    body: requestBody,\n    failOnStatusCode: false\n  }).then(response => {\n    // Adiciona detalhes da API ao Allure\n    cy.allureApiStep('Validação de CPF duplicado', 'POST', url, requestBody, response);\n    cy.allure().step('Validar resposta da API', () => {\n      expect(response.status).to.eq(400);\n      expect(response.body).to.have.property('code', '100762');\n      expect(response.body).to.have.property('message', 'CPF já está cadastrado.');\n    });\n  });\n});", "err": {}, "uuid": "6f6c1f02-10dc-4084-818e-3093f5e34473", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 200 e os dados mascarados do usuário", "fullTitle": "Cadastro Deve retornar 200 e os dados mascarados do usuário", "timedOut": null, "duration": 561, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf',\n  body: {\n    cpf: \"53655184972\"\n  }\n}).then(response => {\n  expect(response.status).to.eq(200);\n  expect(response.body).to.have.property('data');\n  expect(response.body.data).to.have.property('cpf', '53655184972');\n  expect(response.body.data).to.have.property('maskedName').and.to.match(/^\\w+/);\n  expect(response.body.data).to.have.property('birthDate').and.to.not.be.empty;\n});", "err": {}, "uuid": "14d84711-76e2-41a6-acb4-17372664d881", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 422 e mensagem de documento inválido", "fullTitle": "Cadastro Deve retornar 422 e mensagem de documento inválido", "timedOut": null, "duration": 378, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf',\n  failOnStatusCode: false,\n  body: {\n    cpf: \"80807788090\"\n  }\n}).then(response => {\n  expect(response.status).to.eq(422);\n  expect(response.body).to.have.property('code', 'INVALID_DOCUMENT');\n  expect(response.body).to.have.property('message', 'O documento solicitado não está em um formato válido.');\n});", "err": {}, "uuid": "0083b5fa-3274-41f2-ad28-7983cd3dfaa1", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 400 e mensagem ao enviar e-mail inválido", "fullTitle": "Cadastro Deve retornar 400 e mensagem ao enviar e-mail inválido", "timedOut": null, "duration": 171, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/email',\n  failOnStatusCode: false,\n  body: {\n    email: 'teste'\n  }\n}).then(({\n  status,\n  body,\n  headers\n}) => {\n  expect(status).to.eq(400);\n  expect(body).to.have.property('code', '100164');\n  expect(body).to.have.property('message').and.match(/Dados inválidos/i);\n  expect(headers).to.have.property('content-type').and.match(/application\\/json/i);\n});", "err": {}, "uuid": "39673a2a-9123-4342-b74f-46cccdebb89e", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar que o e-mail já está cadastrado", "fullTitle": "Cadastro Deve retornar que o e-mail já está cadastrado", "timedOut": null, "duration": 227, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/email',\n  body: {\n    email: \"<EMAIL>\"\n  }\n}).then(response => {\n  expect(response.status).to.eq(200);\n  expect(response.body.data.isUnique).to.be.false;\n});", "err": {}, "uuid": "45f402a6-a7a5-4c13-ab26-1d867b2747d3", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve validar número de telefone com sucesso", "fullTitle": "Cadastro Deve validar número de telefone com sucesso", "timedOut": null, "duration": 175, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',\n  body: {\n    isdCode: \"55\",\n    telephoneNumber: \"41121321231\"\n  }\n}).then(response => {\n  expect(response.status).to.eq(200);\n  expect(response.body.data.isValid).to.be.true;\n});", "err": {}, "uuid": "938516d2-4054-45cb-9e52-711fa0a62a16", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 400 quando o número de telefone já está cadastrado", "fullTitle": "Cadastro Deve retornar 400 quando o número de telefone já está cadastrado", "timedOut": null, "duration": 164, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',\n  failOnStatusCode: false,\n  body: {\n    isdCode: \"55\",\n    telephoneNumber: \"11111111111\"\n  }\n}).then(({\n  status,\n  body\n}) => {\n  expect(status).to.eq(400);\n  expect(body).to.have.property('code', '100294');\n  expect(body).to.have.property('message', 'Esse número de celular já está cadastrado.');\n});", "err": {}, "uuid": "62cf6306-a203-4bfa-8575-97aff7a9fdf4", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 400 quando o número de telefone não é válido", "fullTitle": "Cadastro Deve retornar 400 quando o número de telefone não é válido", "timedOut": null, "duration": 130, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',\n  failOnStatusCode: false,\n  body: {\n    isdCode: \"55\",\n    telephoneNumber: \"teste\"\n  }\n}).then(({\n  status,\n  body\n}) => {\n  expect(status).to.eq(400);\n  expect(body).to.have.property('code', '100164');\n  expect(body).to.have.property('message', 'Invalid Brazilian telephone number');\n});", "err": {}, "uuid": "d3401700-7ec8-4b9a-8a87-4453d6e43813", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}, {"title": "Deve retornar 400 quando o codigo internacional não é válido", "fullTitle": "Cadastro Deve retornar 400 quando o codigo internacional não é válido", "timedOut": null, "duration": 82, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.api({\n  method: 'POST',\n  url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',\n  failOnStatusCode: false,\n  body: {\n    isdCode: \"XX\",\n    telephoneNumber: \"teste\"\n  }\n}).then(({\n  status,\n  body\n}) => {\n  expect(status).to.eq(400);\n  expect(body).to.have.property('code', '100164');\n  expect(body).to.have.property('message', 'Invalid international telephone number');\n});", "err": {}, "uuid": "834d6aad-4742-4b2f-bcde-68ad70e22409", "parentUUID": "18af09e4-8865-4add-b74d-9070296de084", "isHook": false, "skipped": false}], "suites": [], "passes": ["e972b9dc-8ada-43f0-87a2-919c18e0c007", "cb99b027-2bb7-4bd2-97a5-ce51b80d6683", "6f6c1f02-10dc-4084-818e-3093f5e34473", "14d84711-76e2-41a6-acb4-17372664d881", "0083b5fa-3274-41f2-ad28-7983cd3dfaa1", "39673a2a-9123-4342-b74f-46cccdebb89e", "45f402a6-a7a5-4c13-ab26-1d867b2747d3", "938516d2-4054-45cb-9e52-711fa0a62a16", "62cf6306-a203-4bfa-8575-97aff7a9fdf4", "d3401700-7ec8-4b9a-8a87-4453d6e43813", "834d6aad-4742-4b2f-bcde-68ad70e22409"], "failures": [], "pending": [], "skipped": [], "duration": 2349, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
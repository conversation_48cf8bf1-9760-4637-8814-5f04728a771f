<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:1,&quot;tests&quot;:11,&quot;passes&quot;:11,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-08-18T12:09:28.346Z&quot;,&quot;end&quot;:&quot;2025-08-18T12:09:31.456Z&quot;,&quot;duration&quot;:3110,&quot;testsRegistered&quot;:11,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;5e43578c-77e6-4f0b-9ce9-7fe4e8bfd223&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Cadastro.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Cadastro.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;title&quot;:&quot;Cadastro&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Deve validar elementos no cadastro via botão do header&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve validar elementos no cadastro via botão do header&quot;,&quot;duration&quot;:242,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;// Anotações do Allure Report\ncy.allure().epic(&#x27;Frontend&#x27;).feature(&#x27;Cadastro de Usuário&#x27;).story(&#x27;Validação de Elementos da Tela&#x27;).severity(&#x27;critical&#x27;).tag(&#x27;smoke&#x27;, &#x27;ui&#x27;, &#x27;cadastro&#x27;).description(&#x27;Valida se todos os elementos da tela de cadastro estão visíveis e funcionais quando acessada via botão do header&#x27;).issue(&#x27;JIRA-123&#x27;).tms(&#x27;TC-001&#x27;);\n(0, _blockTrackers.blockTrackers)();\ncy.allure().step(&#x27;Navegar para a tela de cadastro via header&#x27;, () =&gt; {\n  cy.navigateToRegistration(&#x27;header&#x27;);\n});\ncy.allure().step(&#x27;Validar todos os elementos da tela de cadastro&#x27;, () =&gt; {\n  cy.validateRegistrationPageElements();\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;e972b9dc-8ada-43f0-87a2-919c18e0c007&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar elementos no cadastro via botão da home&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve validar elementos no cadastro via botão da home&quot;,&quot;duration&quot;:172,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;// Anotações do Allure Report\ncy.allure().epic(&#x27;Frontend&#x27;).feature(&#x27;Cadastro de Usuário&#x27;).story(&#x27;Validação de Elementos da Tela&#x27;).severity(&#x27;normal&#x27;).tag(&#x27;ui&#x27;, &#x27;cadastro&#x27;, &#x27;home&#x27;).description(&#x27;Valida se todos os elementos da tela de cadastro estão visíveis quando acessada via botão da página inicial&#x27;);\n(0, _blockTrackers.blockTrackers)();\ncy.allure().step(&#x27;Navegar para a tela de cadastro via botão da home&#x27;, () =&gt; {\n  cy.navigateToRegistration(&#x27;home&#x27;);\n});\ncy.allure().step(&#x27;Validar todos os elementos da tela de cadastro&#x27;, () =&gt; {\n  cy.validateRegistrationPageElements();\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;cb99b027-2bb7-4bd2-97a5-ce51b80d6683&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 400 quando CPF já está cadastrado&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 400 quando CPF já está cadastrado&quot;,&quot;duration&quot;:47,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;// Anotações do Allure Report\ncy.allure().epic(&#x27;Backend&#x27;).feature(&#x27;Validação de CPF&#x27;).story(&#x27;CPF Duplicado&#x27;).severity(&#x27;critical&#x27;).tag(&#x27;api&#x27;, &#x27;validation&#x27;, &#x27;cpf&#x27;).description(&#x27;Valida que a API retorna erro 400 quando um CPF já cadastrado é enviado&#x27;).issue(&#x27;JIRA-456&#x27;).tms(&#x27;TC-002&#x27;);\ncy.intercept(&#x27;POST&#x27;, &#x27;**/validate/cpf&#x27;).as(&#x27;validateCpf&#x27;);\nconst requestBody = {\n  cpf: \&quot;11983538647\&quot;\n};\nconst url = &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/cpf&#x27;;\ncy.allure().step(&#x27;Enviar requisição para validar CPF já cadastrado&#x27;, () =&gt; {\n  cy.api({\n    method: &#x27;POST&#x27;,\n    url: url,\n    body: requestBody,\n    failOnStatusCode: false\n  }).then(response =&gt; {\n    // Adiciona detalhes da API ao Allure\n    cy.allureApiStep(&#x27;Validação de CPF duplicado&#x27;, &#x27;POST&#x27;, url, requestBody, response);\n    cy.allure().step(&#x27;Validar resposta da API&#x27;, () =&gt; {\n      expect(response.status).to.eq(400);\n      expect(response.body).to.have.property(&#x27;code&#x27;, &#x27;100762&#x27;);\n      expect(response.body).to.have.property(&#x27;message&#x27;, &#x27;CPF já está cadastrado.&#x27;);\n    });\n  });\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;6f6c1f02-10dc-4084-818e-3093f5e34473&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 200 e os dados mascarados do usuário&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 200 e os dados mascarados do usuário&quot;,&quot;duration&quot;:561,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf&#x27;,\n  body: {\n    cpf: \&quot;53655184972\&quot;\n  }\n}).then(response =&gt; {\n  expect(response.status).to.eq(200);\n  expect(response.body).to.have.property(&#x27;data&#x27;);\n  expect(response.body.data).to.have.property(&#x27;cpf&#x27;, &#x27;53655184972&#x27;);\n  expect(response.body.data).to.have.property(&#x27;maskedName&#x27;).and.to.match(/^\\w+/);\n  expect(response.body.data).to.have.property(&#x27;birthDate&#x27;).and.to.not.be.empty;\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;14d84711-76e2-41a6-acb4-17372664d881&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 422 e mensagem de documento inválido&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 422 e mensagem de documento inválido&quot;,&quot;duration&quot;:378,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf&#x27;,\n  failOnStatusCode: false,\n  body: {\n    cpf: \&quot;80807788090\&quot;\n  }\n}).then(response =&gt; {\n  expect(response.status).to.eq(422);\n  expect(response.body).to.have.property(&#x27;code&#x27;, &#x27;INVALID_DOCUMENT&#x27;);\n  expect(response.body).to.have.property(&#x27;message&#x27;, &#x27;O documento solicitado não está em um formato válido.&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;0083b5fa-3274-41f2-ad28-7983cd3dfaa1&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 400 e mensagem ao enviar e-mail inválido&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 400 e mensagem ao enviar e-mail inválido&quot;,&quot;duration&quot;:171,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/email&#x27;,\n  failOnStatusCode: false,\n  body: {\n    email: &#x27;teste&#x27;\n  }\n}).then(({\n  status,\n  body,\n  headers\n}) =&gt; {\n  expect(status).to.eq(400);\n  expect(body).to.have.property(&#x27;code&#x27;, &#x27;100164&#x27;);\n  expect(body).to.have.property(&#x27;message&#x27;).and.match(/Dados inválidos/i);\n  expect(headers).to.have.property(&#x27;content-type&#x27;).and.match(/application\\/json/i);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;39673a2a-9123-4342-b74f-46cccdebb89e&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar que o e-mail já está cadastrado&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar que o e-mail já está cadastrado&quot;,&quot;duration&quot;:227,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/email&#x27;,\n  body: {\n    email: \&quot;<EMAIL>\&quot;\n  }\n}).then(response =&gt; {\n  expect(response.status).to.eq(200);\n  expect(response.body.data.isUnique).to.be.false;\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;45f402a6-a7a5-4c13-ab26-1d867b2747d3&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve validar número de telefone com sucesso&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve validar número de telefone com sucesso&quot;,&quot;duration&quot;:175,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber&#x27;,\n  body: {\n    isdCode: \&quot;55\&quot;,\n    telephoneNumber: \&quot;41121321231\&quot;\n  }\n}).then(response =&gt; {\n  expect(response.status).to.eq(200);\n  expect(response.body.data.isValid).to.be.true;\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;938516d2-4054-45cb-9e52-711fa0a62a16&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 400 quando o número de telefone já está cadastrado&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 400 quando o número de telefone já está cadastrado&quot;,&quot;duration&quot;:164,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber&#x27;,\n  failOnStatusCode: false,\n  body: {\n    isdCode: \&quot;55\&quot;,\n    telephoneNumber: \&quot;11111111111\&quot;\n  }\n}).then(({\n  status,\n  body\n}) =&gt; {\n  expect(status).to.eq(400);\n  expect(body).to.have.property(&#x27;code&#x27;, &#x27;100294&#x27;);\n  expect(body).to.have.property(&#x27;message&#x27;, &#x27;Esse número de celular já está cadastrado.&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;62cf6306-a203-4bfa-8575-97aff7a9fdf4&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 400 quando o número de telefone não é válido&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 400 quando o número de telefone não é válido&quot;,&quot;duration&quot;:130,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber&#x27;,\n  failOnStatusCode: false,\n  body: {\n    isdCode: \&quot;55\&quot;,\n    telephoneNumber: \&quot;teste\&quot;\n  }\n}).then(({\n  status,\n  body\n}) =&gt; {\n  expect(status).to.eq(400);\n  expect(body).to.have.property(&#x27;code&#x27;, &#x27;100164&#x27;);\n  expect(body).to.have.property(&#x27;message&#x27;, &#x27;Invalid Brazilian telephone number&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;d3401700-7ec8-4b9a-8a87-4453d6e43813&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false},{&quot;title&quot;:&quot;Deve retornar 400 quando o codigo internacional não é válido&quot;,&quot;fullTitle&quot;:&quot;Cadastro Deve retornar 400 quando o codigo internacional não é válido&quot;,&quot;duration&quot;:82,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;fast&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.api({\n  method: &#x27;POST&#x27;,\n  url: &#x27;https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber&#x27;,\n  failOnStatusCode: false,\n  body: {\n    isdCode: \&quot;XX\&quot;,\n    telephoneNumber: \&quot;teste\&quot;\n  }\n}).then(({\n  status,\n  body\n}) =&gt; {\n  expect(status).to.eq(400);\n  expect(body).to.have.property(&#x27;code&#x27;, &#x27;100164&#x27;);\n  expect(body).to.have.property(&#x27;message&#x27;, &#x27;Invalid international telephone number&#x27;);\n});&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;834d6aad-4742-4b2f-bcde-68ad70e22409&quot;,&quot;parentUUID&quot;:&quot;18af09e4-8865-4add-b74d-9070296de084&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;e972b9dc-8ada-43f0-87a2-919c18e0c007&quot;,&quot;cb99b027-2bb7-4bd2-97a5-ce51b80d6683&quot;,&quot;6f6c1f02-10dc-4084-818e-3093f5e34473&quot;,&quot;14d84711-76e2-41a6-acb4-17372664d881&quot;,&quot;0083b5fa-3274-41f2-ad28-7983cd3dfaa1&quot;,&quot;39673a2a-9123-4342-b74f-46cccdebb89e&quot;,&quot;45f402a6-a7a5-4c13-ab26-1d867b2747d3&quot;,&quot;938516d2-4054-45cb-9e52-711fa0a62a16&quot;,&quot;62cf6306-a203-4bfa-8575-97aff7a9fdf4&quot;,&quot;d3401700-7ec8-4b9a-8a87-4453d6e43813&quot;,&quot;834d6aad-4742-4b2f-bcde-68ad70e22409&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:2349,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;overwrite&quot;:true,&quot;html&quot;:true,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;cypress\\results\\assets&quot;,&quot;jsonFile&quot;:&quot;C:\\Users\\<USER>\\OneDrive - uds.com.br\\Arquivos de Jean Ottoni - QA - UDS\\Projetos\\EstrelaBet\\EstrelaBet\\cypress\\results\\Cadastro-report-2025-08-18T090931-0300.json&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\OneDrive - uds.com.br\\Arquivos de Jean Ottoni - QA - UDS\\Projetos\\EstrelaBet\\EstrelaBet\\cypress\\results\\Cadastro-report-2025-08-18T090931-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>
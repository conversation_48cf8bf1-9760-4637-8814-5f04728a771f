const Messages = {
   API_ERRORS: {
        UNAUTHORIZED: "Usuário não autorizado.",
        NOT_FOUND: "Recurso não encontrado.",
        SERVER_ERROR: "Erro interno no servidor. Tente novamente mais tarde.",
        INVALID_CREDENTIALS: "Credenciais inválidas.",
        INVALID_EMAIL: "E-mail inválido.",
    },

    FRONT_ERRORS: {
        LOGIN_PAGE:{
            LOGIN_INVALIDO: "Usuário ou senha incorretos. Verifique e tente novamente.",
            EMAIL_INVALIDO: "O email é inválido.",
            EMAIL_DOIS_CARACTERES: 'Login deve ter pelo menos 3 caracteres',
            SENHA_QTD_INCORRETA: 'Senha deve ter pelo menos 8 caracteres',
        }
    },

    VALIDATIONS: {
        TITLE_PAGE: {
            TITLE: "Bet Online - Casa de Aposta e Cassino | Estrela Bet"
        },
        LOGIN_PAGE: {
            TITLE: "Que bom que você voltou!",
            SUBTITLE: "Informe seus dados para acessar sua conta.",
            EMAIL_TITULO: 'Endereço de e-mail',
            PLACEHOLDER_EMAIL: 'Digite o e-mail',
            PLACEHOLDER_SENHA: 'Digite sua Senha',
            SENHA_TITULO: 'Senha'
        },
        RECUPERAR_SENHA: {
            BOTÃO_RECUPERAR_SENHA: 'Esqueceu a senha?'
        }
    }
};

export default Messages;

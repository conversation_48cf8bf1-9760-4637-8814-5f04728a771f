{"uuid": "a3d04654-28e6-4a2f-87d4-ff2996c5a764", "historyId": "2f6a9fbde0b17c8684d2b47faf68fcd6", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968581, "name": "Navegar para a tela de cadastro via header", "stop": 1755518968582}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518968582, "name": "Validar todos os elementos da tela de cadastro", "stop": 1755518968584}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}, {"name": "epic", "value": "Frontend"}, {"name": "feature", "value": "Cadastro de Usuário"}, {"name": "story", "value": "Validação de Elementos da Tela"}, {"name": "severity", "value": "critical"}, {"name": "tag", "value": "smoke"}, {"name": "tag", "value": "ui"}, {"name": "tag", "value": "cadastro"}], "links": [{"name": "JIRA-123", "url": "https://jira.company.com/browse/{}undefined", "type": "issue"}, {"name": "TC-001", "url": "https://testmanagement.company.com/testcase/{}undefined", "type": "tms"}], "start": 1755518968407, "name": "Deve validar elementos no cadastro via botão do header", "fullName": "Deve validar elementos no cadastro via botão do header", "description": "Valida se todos os elementos da tela de cadastro estão visíveis e funcionais quando acessada via botão do header", "stop": 1755518968584}
/// <reference types="cypress" />

import loc from '../../support/locators'
import { Utility } from "../../support/utility"
import messages from '../../support/validationMessages'
import '@cypress/grep';
import { blockTrackers } from '../../support/blockTrackers';

const url = new Utility().getBaseUrl();
const urlRequest = new Utility().getApiUrl();

describe('Cadastro', () => {

    it.only('Deve validar elemento no cadstro de usuário', () => {
        blockTrackers();
        cy.visit(url)
        cy.contains('button', 'Cadastrar')

        cy.get(':nth-child(3) > .nebulosa-header__buttomAndBadgeWrapper > .nebulosa-button__root')
            .click()

        cy.log('Validando elementos da tela de cadastro')
        // Verifica se o botão Suporte está visível e clica nele
        cy.isVisible('button', 'Suporte')
        cy.isVisible('img[alt="Logo-estrelabet"]')
        cy.validateText('.gap_xs > .d_none', 'Vamos começar seu cadastro')

        cy.isVisible('input[name="nationalId"]')
        cy.validatePlaceholder('input[name="nationalId"]', 'CPF *')
        cy.contains('button', 'Verificar')
            .should('be.visible')
            .and('have.attr', 'type', 'button')

        cy.isVisible('input[name="emailId"]')
        cy.validatePlaceholder('input[name="emailId"]', 'E-mail *')

        cy.isVisible('input[name="areaCode"]')
        cy.validatePlaceholder('input[name="areaCode"]', 'Ex: +55')

        cy.isVisible('input[name="phoneNumber"]')
        cy.validatePlaceholder('input[name="phoneNumber"]', 'Celular *')

        cy.isVisible('input[name="password"]')
        cy.validatePlaceholder('input[name="password"]', 'Senha *')

        //Valida o checkbox
        cy.get('button#termsAndConditions')
            .should('have.attr', 'role', 'checkbox')
            .and('have.attr', 'aria-checked', 'false')
            .and('be.visible');

        //Valida o texto (ajuste o seletor para o seu caso real)
        cy.isVisible('p', 'Confirmo que tenho mais de 18 anos e aceito')

        //Valida os links de Termos e Política
        cy.get('a[href="/pb/politica/termos-e-condicoes"]')
            .should('be.visible')
            .and('contain', 'Termos e condições')
        cy.get('a[href="/pb/policy/privacy-policy"]')
            .should('be.visible')
            .and('contain', 'Política de privacidade')

        cy.isVisible('button', 'Concluir cadastro')

        cy.isVisible('p', 'Já possui uma conta?')
        cy.get('a[href="/pb/login"]')
            .should('be.visible')
            .and('contain', 'Entrar')

        cy.log('Voltando para a página inicial')
        cy.go('back')
        cy.visit(url)

        cy.log('Valida botão cadastre-se')
        cy.isVisible('button', 'Cadastre-se')
        cy.get('button.cadastro-home-gtm')
            .contains('Cadastre-se')
            .should('be.visible')
            .click()

        cy.log('Validando elementos da tela de cadastro')
        // Verifica se o botão Suporte está visível e clica nele
        cy.isVisible('button', 'Suporte')
        cy.isVisible('img[alt="Logo-estrelabet"]')
        cy.validateText('.gap_xs > .d_none', 'Vamos começar seu cadastro')

        cy.isVisible('input[name="nationalId"]')
        cy.validatePlaceholder('input[name="nationalId"]', 'CPF *')
        cy.contains('button', 'Verificar')
            .should('be.visible')
            .and('have.attr', 'type', 'button')

        cy.isVisible('input[name="emailId"]')
        cy.validatePlaceholder('input[name="emailId"]', 'E-mail *')

        cy.isVisible('input[name="areaCode"]')
        cy.validatePlaceholder('input[name="areaCode"]', 'Ex: +55')

        cy.isVisible('input[name="phoneNumber"]')
        cy.validatePlaceholder('input[name="phoneNumber"]', 'Celular *')

        cy.isVisible('input[name="password"]')
        cy.validatePlaceholder('input[name="password"]', 'Senha *')

        //Valida o checkbox
        cy.get('button#termsAndConditions')
            .should('have.attr', 'role', 'checkbox')
            .and('have.attr', 'aria-checked', 'false')
            .and('be.visible');

        //Valida o texto (ajuste o seletor para o seu caso real)
        cy.isVisible('p', 'Confirmo que tenho mais de 18 anos e aceito')

        //Valida os links de Termos e Política
        cy.get('a[href="/pb/politica/termos-e-condicoes"]')
            .should('be.visible')
            .and('contain', 'Termos e condições')
        cy.get('a[href="/pb/policy/privacy-policy"]')
            .should('be.visible')
            .and('contain', 'Política de privacidade')

        cy.isVisible('button', 'Concluir cadastro')

        cy.isVisible('p', 'Já possui uma conta?')
        cy.get('a[href="/pb/login"]')
            .should('be.visible')
            .and('contain', 'Entrar')

    })
    it('Deve retornar 400 quando CPF já está cadastrado', () => {
        cy.intercept('POST', '**/validate/cpf').as('validateCpf')
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/cpf',
            body: {
                cpf: "11983538647"
            },
            failOnStatusCode: false  // permite validar erros sem quebrar o teste
        }).then((response) => {
            expect(response.status).to.eq(400)
            expect(response.body).to.have.property('code', '100762')
            expect(response.body).to.have.property('message', 'CPF já está cadastrado.')
        })
    })
    it('Deve retornar 200 e os dados mascarados do usuário', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf',
            body: {
                cpf: "53655184972"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body).to.have.property('data')
            expect(response.body.data).to.have.property('cpf', '53655184972')
            expect(response.body.data).to.have.property('maskedName').and.to.match(/^\w+/)
            expect(response.body.data).to.have.property('birthDate').and.to.not.be.empty
        })
    })
    it('Deve retornar 422 e mensagem de documento inválido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/v2/registration/validate/cpf',
            failOnStatusCode: false,
            body: {
                cpf: "80807788090"
            }
        }).then((response) => {
            expect(response.status).to.eq(422)
            expect(response.body).to.have.property('code', 'INVALID_DOCUMENT')
            expect(response.body).to.have.property('message', 'O documento solicitado não está em um formato válido.')
        })
    })
    it('Deve retornar 400 e mensagem ao enviar e-mail inválido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/email',
            failOnStatusCode: false,
            body: {
                email: 'teste'
            }
        }).then(({ status, body, headers }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message').and.match(/Dados inválidos/i)
            expect(headers).to.have.property('content-type').and.match(/application\/json/i)
        })
    })
    it('Deve retornar que o e-mail já está cadastrado', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/email',
            body: {
                email: "<EMAIL>"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body.data.isUnique).to.be.false
        })
    })
    it('Deve validar número de telefone com sucesso', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            body: {
                isdCode: "55",
                telephoneNumber: "41121321231"
            }
        }).then((response) => {
            expect(response.status).to.eq(200)
            expect(response.body.data.isValid).to.be.true
        })
    })
    it('Deve retornar 400 quando o número de telefone já está cadastrado', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "55",
                telephoneNumber: "11111111111"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100294')
            expect(body).to.have.property('message', 'Esse número de celular já está cadastrado.')
        })
    })
    it('Deve retornar 400 quando o número de telefone não é válido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "55",
                telephoneNumber: "teste"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message', 'Invalid Brazilian telephone number')
        })
    })
    it('Deve retornar 400 quando o codigo internacional não é válido', () => {
        cy.api({
            method: 'POST',
            url: 'https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber',
            failOnStatusCode: false,
            body: {
                isdCode: "XX",
                telephoneNumber: "teste"
            }
        }).then(({ status, body }) => {
            expect(status).to.eq(400)
            expect(body).to.have.property('code', '100164')
            expect(body).to.have.property('message', 'Invalid international telephone number')
        })
    })
    //it('Cadastrar usuario', () => { }) 
})
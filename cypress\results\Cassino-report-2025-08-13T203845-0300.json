{"stats": {"suites": 1, "tests": 3, "passes": 3, "pending": 0, "failures": 0, "start": "2025-08-13T23:37:14.519Z", "end": "2025-08-13T23:38:45.924Z", "duration": 91405, "testsRegistered": 3, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "4a14c0ae-df4b-4f41-b513-c5c37a710b17", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\Cassino.js", "file": "cypress\\e2e\\FrontEnd\\Cassino.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "22590d79-8f12-4f05-8ebb-8f68ed8ed7aa", "title": "Deve abrir a página de cassino", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Acessa pagina de Cassino e valida elementos", "fullTitle": "Deve abrir a página de cassino Acessa pagina de Cassino e valida elementos", "timedOut": null, "duration": 46884, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.contains('a', '<PERSON><PERSON>');\ncy.contains('a', '<PERSON><PERSON>').click();\n// Valida o botão de busca (ícone de lupa)\ncy.get('a[href=\"/jogos/busca\"]').should('be.visible');\n// Lista de labels esperadas\nconst botoes = ['Inicial', 'Torneios', 'Slots', 'Crash Games', 'Comprar Bônus', 'Novos Jogos', 'Jackpots', '<PERSON><PERSON>'];\n// Valida cada botão\nbotoes.forEach(label => {\n  cy.contains('.nebulosa-button__buttonLabel', label).should('be.visible');\n});\ncy.clickByContains('.nebulosa-button__buttonLabel', 'Mai<PERSON>');\n// Lista de labels esperadas\nconst categorias = ['Populares', 'Jogos de Fortuna', 'Games Global', 'Estrela Indica', 'Novos Jogos', 'Pragmatic Play', 'Missão Lucky Rush', 'Crash Games', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>lot<PERSON>', 'Jogos Ao Vivo', '<PERSON><PERSON><PERSON>', 'Em breve', 'Provedores'];\n// Valida cada label visível\ncategorias.forEach(label => {\n  cy.contains('li', label).should('be.visible');\n});\n// Fecha o modal (botão X no header)\ncy.get('.nebulosa-modal__HeaderRight .nebulosa-modal__IconAction').click();\n// Valida que o modal fechou\ncy.get('[role=\"dialog\"]').should('not.exist');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Populares');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Jogos de Fortuna');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Estrela Indica');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Novos Jogos');\ncy.clickByContains(':nth-child(5) > .ov-y_hidden > .jc_space-between > .desktop\\\\:ai_center > .jc_start', 'Mais premiados');\ncy.clickByContains(':nth-child(6) > .ov-y_hidden > .jc_space-between > .desktop\\\\:ai_center > .jc_start', 'Menos premiados');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Pragmatic Play');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Crash Games');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Comprar Bônus');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Slots');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Jogos Ao Vivo');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Jackpots');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Em breve');\ncy.clickByContains('div.d_flex > div > div > div > h2', 'Provedores de jogos');", "err": {}, "uuid": "169b1621-b67e-4b49-ba50-870d4e62b9bc", "parentUUID": "22590d79-8f12-4f05-8ebb-8f68ed8ed7aa", "isHook": false, "skipped": false}, {"title": "Acessa pagina de Cassino e acessar um jogo", "fullTitle": "Deve abrir a página de cassino Acessa pagina de Cassino e acessar um jogo", "timedOut": null, "duration": 11726, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.contains('a', '<PERSON><PERSON>');\ncy.contains('a', '<PERSON><PERSON>').click();\n// Acessar jogos\ncy.get('img[alt=\"Aviator-game-img\"]').click();\n//Caso o usuario nao tenha verificado a identidade, clica no botão \"Não quero verificar agora\"\ncy.get('body').then($body => {\n  // Verifica se o botão existe\n  if ($body.find('button:contains(\"Não quero verificar agora\")').length) {\n    cy.contains('button', 'Não quero verificar agora').click();\n  } else {\n    cy.log('Modal não apareceu, continuando o teste...');\n  }\n});", "err": {}, "uuid": "247024cd-2b75-446d-ae21-a3e8a7a16508", "parentUUID": "22590d79-8f12-4f05-8ebb-8f68ed8ed7aa", "isHook": false, "skipped": false}, {"title": "Valida elementos do footer", "fullTitle": "Deve abrir a página de cassino Valida elementos do footer", "timedOut": null, "duration": 1862, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "// \"Aposte\"\ncy.isVisible('.StaticFooter_footer-links-section__0Fi_h', 'Aposte');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/esportes#/overview\"]', 'Apostas esportivas');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/gameplay/fortune-tiger\"]', 'Fortune Tiger');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/gameplay/fortune-rabbit\"]', 'Fortune Rabbit');\n// \"Links úteis\"\ncy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Links úteis');\ncy.isVisible('[href=\"https://comunidade.estrelabet.com/\"]', 'Comunidade');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/promotions/all\"]', 'Promoções');\n// \"Regras\"\ncy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Regras');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/politica/termos-e-condicoes\"]', 'Termos e Condições Gerais');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/page/responsible-gaming\"]', 'Jogo responsável');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/policy/sports-betting-rules\"]', 'Regras de apostas esportivas');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/policy/bonus-rules\"]', 'Termos e condições gerais de bônus');\ncy.isVisible('[href=\"https://www.estrelabet.bet.br/pb/policy/privacy-policy\"]', 'Política de privacidade');\n// \"Suporte\"\ncy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Suporte');\ncy.isVisible('[href=\"https://estrelabet.zendesk.com/hc\"]', 'Central de ajuda');\ncy.isVisible('[href=\"tel:0800 000 4546\"]', '0800 000 4546');\ncy.isVisible('[href=\"mailto:<EMAIL>\"]', '<EMAIL>');\n// \"Outros\"\ncy.isVisible('.StaticFooter_footer-links-header__U2WL_', 'Outros');\ncy.isVisible('[href=\"https://estrela-bet.atlassian.net/helpcenter/ouvidoria/\"]', 'Ouvidoria');\ncy.isVisible('[href=\"https://consumidor2.procon.sp.gov.br/login\"]', 'Procon');\ncy.isVisible('[href=\"https://www.planalto.gov.br/ccivil_03/leis/l8078compilado.htm\"]', 'Código de Defesa do Consumidor');", "err": {}, "uuid": "6f5cd5e5-e6a3-458a-8256-0767829ff10a", "parentUUID": "22590d79-8f12-4f05-8ebb-8f68ed8ed7aa", "isHook": false, "skipped": false}], "suites": [], "passes": ["169b1621-b67e-4b49-ba50-870d4e62b9bc", "247024cd-2b75-446d-ae21-a3e8a7a16508", "6f5cd5e5-e6a3-458a-8256-0767829ff10a"], "failures": [], "pending": [], "skipped": [], "duration": 60472, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
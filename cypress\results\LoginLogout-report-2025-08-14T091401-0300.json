{"stats": {"suites": 3, "tests": 6, "passes": 5, "pending": 0, "failures": 1, "start": "2025-08-14T12:12:41.138Z", "end": "2025-08-14T12:14:01.224Z", "duration": 80086, "testsRegistered": 6, "passPercent": 83.33333333333334, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "16e1d0a6-50a0-4312-91cb-3fd5124e5a20", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "file": "cypress\\e2e\\FrontEnd\\LoginLogout.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "eb35cde3-be63-4347-a0e5-971bbef0a8cd", "title": "<PERSON>e validar o login", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Validando elementos da tela", "fullTitle": "Deve validar o login Validando elementos da tela", "timedOut": null, "duration": 6075, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "//Suporte\ncy.validateText('span.d_block', 'Suporte');\n//Valida logo do cliente\ncy.get(_locators.default.LOGIN.LOGO_CLIENTE).should('be.visible').and('have.attr', 'src').and('match', /\\/logo-estrelabet\\.svg$/);\n//Validação do título e subtítulo\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.LOGIN_PAGE.TITLE);\n//Validando campo email, título e placeholder\ncy.validateText(_locators.default.LOGIN.EMAIL_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.EMAIL_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.EMAIL_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_EMAIL);\n//Validando campo senha, título e placeholder\ncy.validateText(_locators.default.LOGIN.SENHA_TITULO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.SENHA_TITULO);\ncy.validatePlaceholder(_locators.default.LOGIN.SENHA_CAMPO, _validationMessages.default.VALIDATIONS.LOGIN_PAGE.PLACEHOLDER_SENHA);\n// Digitar senha\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type('123456').should('have.attr', 'type', 'password'); // Verifica que está oculta\n// Clicar no botão de mostrar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).should('be.visible').and('exist').click();\n// Verificar que a senha está visível\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'text');\n// Clicar novamente para ocultar senha\ncy.get(_locators.default.LOGIN.ICONE_OLHO_SENHA).click();\n// Verificar que a senha voltou a ser oculta\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).should('have.attr', 'type', 'password');\n//Botão esqueci a senha\ncy.validateTextContains(_validationMessages.default.VALIDATIONS.RECUPERAR_SENHA.BOTÃO_RECUPERAR_SENHA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "6ca2d76a-092d-4dbc-95a7-e227c61be222", "parentUUID": "eb35cde3-be63-4347-a0e5-971bbef0a8cd", "isHook": false, "skipped": false}, {"title": "Deve realizar o login com sucesso", "fullTitle": "Deve validar o login Deve realizar o login com sucesso", "timedOut": null, "duration": 7834, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "const username = Cypress.env('user_name');\nconst password = Cypress.env('user_password');\nexpect(username, 'Nome de usuário').to.be.a('string').and.not.be.empty;\nif (typeof password !== 'string' || !password) {\n  throw new Error('O valor senha está ausente, inclua a senha usando o cy.env');\n}\ncy.get(_locators.default.LOGIN.EMAIL_CAMPO).type(username).should('have.value', username);\ncy.get(_locators.default.LOGIN.SENHA_CAMPO).type(password, {\n  log: false\n}).should(el$ => {\n  if (el$.val() !== password) {\n    throw new Error('Valor diferente da senha digitada');\n  }\n});\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 200);\ncy.get('@login').then(({\n  request,\n  response\n}) => {\n  expect(request.method).to.equal('POST');\n  cy.isVisible('.nebulosa-avatar__fallback:eq(2)');\n});", "err": {}, "uuid": "52de1ab3-9384-4bcf-b6af-9df2d819dd59", "parentUUID": "eb35cde3-be63-4347-a0e5-971bbef0a8cd", "isHook": false, "skipped": false}], "suites": [{"uuid": "498d4a60-5e99-42bb-b816-86436257360a", "title": "Deve validar padrões de email incorreto", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve validar login credenciais incorretas", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar login credenciais incorretas", "timedOut": null, "duration": 7666, "state": "passed", "speed": "medium", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, '<EMAIL>');\ncy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '123456Uds@');\ncy.clickMouse(_locators.default.LOGIN.BOTAO_ENTRAR);\ncy.wait('@login').its('response.statusCode').should('eq', 401);\ncy.validateTextContains(_validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.LOGIN_INVALIDO);", "err": {}, "uuid": "ab1def48-a8b3-4d89-8405-72084eec1f38", "parentUUID": "498d4a60-5e99-42bb-b816-86436257360a", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres no email", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres no email", "timedOut": null, "duration": 2143, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.EMAIL_CAMPO, 'qa');\ncy.get(_locators.default.MENSAGENS_ERRO.EMAIL_INVÁLIDO).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.EMAIL_DOIS_CARACTERES);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "46d78eac-3635-48a6-adc3-d45e4b6c539a", "parentUUID": "498d4a60-5e99-42bb-b816-86436257360a", "isHook": false, "skipped": false}, {"title": "Deve validar qtd mínima de caracteres na senha", "fullTitle": "Deve validar o login Deve validar padrões de email incorreto Deve validar qtd mínima de caracteres na senha", "timedOut": null, "duration": 2221, "state": "passed", "speed": "fast", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.typeText(_locators.default.LOGIN.SENHA_CAMPO, '1234567');\ncy.get(_locators.default.MENSAGENS_ERRO.COMPLEXIDADE_SENHA_INCORRETA).should('have.text', _validationMessages.default.FRONT_ERRORS.LOGIN_PAGE.SENHA_QTD_INCORRETA);\n//Botão entrar desabilitado\ncy.get(_locators.default.LOGIN.BOTAO_ENTRAR).should('have.text', 'Entrar').and('be.visible').and('be.disabled');", "err": {}, "uuid": "62d63c8e-4dbd-4bf9-a45d-5b911e3f0ef9", "parentUUID": "498d4a60-5e99-42bb-b816-86436257360a", "isHook": false, "skipped": false}], "suites": [], "passes": ["ab1def48-a8b3-4d89-8405-72084eec1f38", "46d78eac-3635-48a6-adc3-d45e4b6c539a", "62d63c8e-4dbd-4bf9-a45d-5b911e3f0ef9"], "failures": [], "pending": [], "skipped": [], "duration": 12030, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": ["6ca2d76a-092d-4dbc-95a7-e227c61be222", "52de1ab3-9384-4bcf-b6af-9df2d819dd59"], "failures": [], "pending": [], "skipped": [], "duration": 13909, "root": false, "rootEmpty": false, "_timeout": 2000}, {"uuid": "5e6a0bdc-610f-462c-8495-28f11c4a17d4", "title": "<PERSON>e validar o logout", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Deve realizar o logout", "fullTitle": "Deve validar o logout Deve realizar o logout", "timedOut": null, "duration": 13300, "state": "failed", "speed": null, "pass": false, "fail": true, "pending": false, "context": null, "code": "//Validando avatar\ncy.isVisible('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Abrindo opções do usuário\ncy.clickMouse('.nebulosa-header__desktopButtonWrapper > .nebulosa-avatar__avatarContainer > .nebulosa-avatar__root > .nebulosa-avatar__fallback');\n//Clicando em sair\ncy.contains('section.d_flex > div', 'Sair').should('be.visible').click();\ncy.wait('@logout').its('response.statusCode').should('eq', 200);", "err": {"message": "CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `logout`. No request ever occurred.\n\nhttps://on.cypress.io/wait", "estack": "CypressError: Timed out retrying after 10000ms: `cy.wait()` timed out waiting `10000ms` for the 1st request to the route: `logout`. No request ever occurred.\n\nhttps://on.cypress.io/wait\n    at cypressErr (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:77648:18)\n    at Object.errByPath (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:77702:10)\n    at checkForXhr (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:138962:84)\n    at <unknown> (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:138988:28)\n    at tryCatcher (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1777:23)\n    at Promise.attempt.Promise.try (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:4285:29)\n    at whenStable (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:147344:68)\n    at <unknown> (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:147285:14)\n    at tryCatcher (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1777:23)\n    at Promise._settlePromiseFromHandler (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1489:31)\n    at Promise._settlePromise (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1546:18)\n    at Promise._settlePromise0 (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1591:10)\n    at Promise._settlePromises (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1671:18)\n    at Promise._fulfill (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:1615:18)\n    at <unknown> (https://hml.estrelabet.bet.br/__cypress/runner/cypress_runner.js:5420:46)", "diff": null}, "uuid": "bb82e9a6-2173-4edb-9bec-b25c2a21215d", "parentUUID": "5e6a0bdc-610f-462c-8495-28f11c4a17d4", "isHook": false, "skipped": false}], "suites": [], "passes": [], "failures": ["bb82e9a6-2173-4edb-9bec-b25c2a21215d"], "pending": [], "skipped": [], "duration": 13300, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
<!doctype html>
<html lang="en"><head><meta charSet="utf-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge"/><meta name="viewport" content="width=device-width, initial-scale=1"/><title>Mochawesome Report</title><link rel="stylesheet" href="assets\app.css"/></head><body data-raw="{&quot;stats&quot;:{&quot;suites&quot;:1,&quot;tests&quot;:1,&quot;passes&quot;:1,&quot;pending&quot;:0,&quot;failures&quot;:0,&quot;start&quot;:&quot;2025-08-13T23:22:52.047Z&quot;,&quot;end&quot;:&quot;2025-08-13T23:23:21.942Z&quot;,&quot;duration&quot;:29895,&quot;testsRegistered&quot;:1,&quot;passPercent&quot;:100,&quot;pendingPercent&quot;:0,&quot;other&quot;:0,&quot;hasOther&quot;:false,&quot;skipped&quot;:0,&quot;hasSkipped&quot;:false},&quot;results&quot;:[{&quot;uuid&quot;:&quot;502c3b75-9c88-474f-ac4a-2fba816ed049&quot;,&quot;title&quot;:&quot;&quot;,&quot;fullFile&quot;:&quot;cypress\\e2e\\FrontEnd\\Deposito.js&quot;,&quot;file&quot;:&quot;cypress\\e2e\\FrontEnd\\Deposito.js&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[],&quot;suites&quot;:[{&quot;uuid&quot;:&quot;c4fcef49-f7f8-4298-81df-ad2a1cb9acc1&quot;,&quot;title&quot;:&quot;Deve validar a geração de pix&quot;,&quot;fullFile&quot;:&quot;&quot;,&quot;file&quot;:&quot;&quot;,&quot;beforeHooks&quot;:[],&quot;afterHooks&quot;:[],&quot;tests&quot;:[{&quot;title&quot;:&quot;Gerar pix com sucesso&quot;,&quot;fullTitle&quot;:&quot;Deve validar a geração de pix Gerar pix com sucesso&quot;,&quot;duration&quot;:25803,&quot;state&quot;:&quot;passed&quot;,&quot;speed&quot;:&quot;slow&quot;,&quot;pass&quot;:true,&quot;fail&quot;:false,&quot;pending&quot;:false,&quot;code&quot;:&quot;cy.get(&#x27;.nebulosa-header__buttomAndBadgeWrapper&#x27;).contains(&#x27;button&#x27;, &#x27;Depositar&#x27;);\ncy.clickByContains(&#x27;.nebulosa-header__buttomAndBadgeWrapper&#x27;, &#x27;Depositar&#x27;);\n//Validando modal de valores\n//Título\ncy.contains(&#x27;h1&#x27;, &#x27;Depósito rápido&#x27;).should(&#x27;be.visible&#x27;);\n//Título campo e valor pré selecionado\ncy.validateText(&#x27;.nebulosa-input__Root &gt; label&#x27;, &#x27;Valor do depósito&#x27;);\ncy.get(&#x27;.nebulosa-input__Input &gt; input:eq(1)&#x27;).should(&#x27;have.value&#x27;, &#x27;R$ 50,00&#x27;);\n//Validando opções disponíveis para depósito\ncy.validateText(&#x27;.d_flex &gt; div &gt; h3&#x27;, &#x27;Valor mínimo R$ 1,00 e valor máximo R$ 45.000,00&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(0)&#x27;, &#x27;R$ 10&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(1)&#x27;, &#x27;R$ 50&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(2)&#x27;, &#x27;R$ 100&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(3)&#x27;, &#x27;R$ 250&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(4)&#x27;, &#x27;R$ 500&#x27;);\ncy.validateText(&#x27;[type=\&quot;button\&quot;] &gt; span:eq(5)&#x27;, &#x27;R$ 1.000&#x27;);\n//validando botão e mensagem de maior de 18\ncy.validateText(&#x27;[type=\&quot;submit\&quot;]&#x27;, &#x27;Depositar&#x27;);\n//cy.validateText(&#x27;form &gt; div &gt; .d_flex &gt; .ai_center &gt; p&#x27;, &#x27;Depósitos proibidos para menores de 18 anos.&#x27;)\n//Botão x\ncy.isVisible(&#x27;[data-testid=\&quot;closeDepositModalButton\&quot;]&#x27;);\n//Banner principal\ncy.isVisible(&#x27;img[src*=\&quot;985f43e8-ccfa-4e8f-9770-1144f09b229f\&quot;]&#x27;);\n//clicando para avançar\ncy.clickMouse(&#x27;[type=\&quot;submit\&quot;]&#x27;, &#x27;Depositar&#x27;);\ncy.wait(&#x27;@gerandoQrCode&#x27;, {\n  timeout: 15000\n}).its(&#x27;response.statusCode&#x27;).should(&#x27;eq&#x27;, 200);\n//Label depósito/pix\ncy.clickByContains(&#x27;h1&#x27;, &#x27;Código Pix disponível&#x27;);\ncy.clickByContains(&#x27;p&#x27;, &#x27;Copie o código e utilize o PIX Copia e Cola no aplicativo do seu banco.&#x27;);\ncy.isVisible(&#x27;button&#x27;, &#x27;Saiba como fazer o pagamento com Pix&#x27;);\ncy.clickByContains(&#x27;button&#x27;, &#x27;Saiba como fazer o pagamento com Pix&#x27;);\n//Listagem com a informaços do Saiba como fazer o pagamento com Pix\ncy.get(&#x27;ul &gt; ul&#x27;).eq(0).within(() =&gt; {\n  cy.contains(&#x27;p&#x27;, &#x27;1. Acesse o aplicativo do seu banco e escolha a opção Pix Copia e Cola” e cole o código&#x27;).should(&#x27;be.visible&#x27;);\n});\ncy.get(&#x27;ul &gt; ul&#x27;).eq(1).within(() =&gt; {\n  cy.contains(&#x27;p&#x27;, &#x27;2. Confirme as informações e finalize o pagamento&#x27;).should(&#x27;be.visible&#x27;);\n});\ncy.get(&#x27;ul &gt; ul&#x27;).eq(2).within(() =&gt; {\n  cy.contains(&#x27;p&#x27;, &#x27;3. Seu pagamento será aprovado em alguns segundos!&#x27;).should(&#x27;be.visible&#x27;);\n});\ncy.isVisible(&#x27;p&#x27;, &#x27;Pagamentos feitos por outro CPF ou de contas empresariais serão automaticamente rejeitados&#x27;);\ncy.isVisible(&#x27;button&#x27;, &#x27;Exibir QR Code&#x27;);\ncy.clickByContains(&#x27;button&#x27;, &#x27;Exibir QR Code&#x27;);\ncy.get(&#x27;img&#x27;).should(&#x27;exist&#x27;);\n// Captura o valor inicial do contador\ncy.get(&#x27;.pt_xxxs &gt; .d_flex &gt; .ai_center &gt; div.fw_regular&#x27;).invoke(&#x27;text&#x27;).then(initialValue =&gt; {\n  // Espera 1 segundo (ou o tempo que o contador deve mudar)\n  cy.wait(1000);\n  // Captura novamente e compara\n  cy.get(&#x27;.pt_xxxs &gt; .d_flex &gt; .ai_center &gt; div.fw_regular&#x27;).invoke(&#x27;text&#x27;).should(newValue =&gt; {\n    expect(newValue).to.not.eq(initialValue);\n  });\n});\ncy.validateText(&#x27;.gap_md &gt; .mb_xxxs &gt; .fs_xxs&#x27;, &#x27;Valor do depósito:&#x27;);\ncy.wait(2000);\ncy.contains(&#x27;.mb_xxxs &gt; .fs_xs&#x27;, &#x27;R$ 50,00&#x27;).should(&#x27;be.visible&#x27;);\n//botao Copiar código Pix\ncy.contains(&#x27;button&#x27;, &#x27;Copiar código Pix&#x27;).should(&#x27;have.attr&#x27;, &#x27;lefticon&#x27;, &#x27;brand-pix&#x27;);\ncy.stubClipboard();\ncy.get(&#x27;[lefticon=\&quot;brand-pix\&quot;] &gt; span&#x27;).click();\ncy.contains(&#x27;Código PIX copiado!&#x27;).should(&#x27;be.visible&#x27;);&quot;,&quot;err&quot;:{},&quot;uuid&quot;:&quot;5a2b9e67-80e5-429d-a9ca-************&quot;,&quot;parentUUID&quot;:&quot;c4fcef49-f7f8-4298-81df-ad2a1cb9acc1&quot;,&quot;isHook&quot;:false,&quot;skipped&quot;:false}],&quot;suites&quot;:[],&quot;passes&quot;:[&quot;5a2b9e67-80e5-429d-a9ca-************&quot;],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:25803,&quot;root&quot;:false,&quot;rootEmpty&quot;:false,&quot;_timeout&quot;:2000}],&quot;passes&quot;:[],&quot;failures&quot;:[],&quot;pending&quot;:[],&quot;skipped&quot;:[],&quot;duration&quot;:0,&quot;root&quot;:true,&quot;rootEmpty&quot;:true,&quot;_timeout&quot;:2000}],&quot;meta&quot;:{&quot;mocha&quot;:{&quot;version&quot;:&quot;7.2.0&quot;},&quot;mochawesome&quot;:{&quot;options&quot;:{&quot;quiet&quot;:false,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;saveHtml&quot;:true,&quot;saveJson&quot;:true,&quot;consoleReporter&quot;:&quot;spec&quot;,&quot;useInlineDiffs&quot;:false,&quot;code&quot;:true},&quot;version&quot;:&quot;7.1.3&quot;},&quot;marge&quot;:{&quot;options&quot;:{&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;overwrite&quot;:true,&quot;html&quot;:true,&quot;json&quot;:true,&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;},&quot;version&quot;:&quot;6.2.0&quot;}}}" data-config="{&quot;reportFilename&quot;:&quot;[name]-report-[datetime]&quot;,&quot;reportDir&quot;:&quot;cypress/results&quot;,&quot;reportTitle&quot;:&quot;EstrelaBet&quot;,&quot;reportPageTitle&quot;:&quot;Mochawesome Report&quot;,&quot;inline&quot;:false,&quot;inlineAssets&quot;:false,&quot;cdn&quot;:false,&quot;charts&quot;:false,&quot;enableCharts&quot;:false,&quot;code&quot;:true,&quot;enableCode&quot;:true,&quot;autoOpen&quot;:false,&quot;overwrite&quot;:true,&quot;timestamp&quot;:false,&quot;ts&quot;:false,&quot;showPassed&quot;:true,&quot;showFailed&quot;:true,&quot;showPending&quot;:true,&quot;showSkipped&quot;:false,&quot;showHooks&quot;:&quot;failed&quot;,&quot;saveJson&quot;:true,&quot;saveHtml&quot;:true,&quot;dev&quot;:false,&quot;assetsDir&quot;:&quot;cypress\\results\\assets&quot;,&quot;jsonFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\Deposito-report-2025-08-13T202321-0300.json&quot;,&quot;htmlFile&quot;:&quot;C:\\Users\\<USER>\\Documents\\EstrelaAllan\\EstrelaBet\\cypress\\results\\Deposito-report-2025-08-13T202321-0300.html&quot;}"><div id="report"></div><script src="assets\app.js"></script></body></html>
// Import commands.js using ES2015 syntax:
import './commands'
const registerCypressGrep = require('@cypress/grep');
registerCypressGrep();
import 'cypress-plugin-api';
import '@shelex/cypress-allure-plugin';

Cypress.on('window:before:load', (win) => {
  Object.defineProperty(win.navigator, 'userAgent', {
    value: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36',
  });
});

Cypress.on('uncaught:exception', () => {
  return false
})

Cypress.on('log:added', (event) => {
  // Impede o log de "xhr" e "fetch" diretamente na interface do Cypress
  if (event.name === 'xhr' || event.name === 'fetch') {
    event.set({ log: false }) // Isso desativa o log de requisições
  }
})
{"stats": {"suites": 1, "tests": 1, "passes": 1, "pending": 0, "failures": 0, "start": "2025-08-13T23:38:52.010Z", "end": "2025-08-13T23:39:31.736Z", "duration": 39726, "testsRegistered": 1, "passPercent": 100, "pendingPercent": 0, "other": 0, "hasOther": false, "skipped": 0, "hasSkipped": false}, "results": [{"uuid": "c7b0612f-a7ee-431e-bc8a-143b374b1815", "title": "", "fullFile": "cypress\\e2e\\FrontEnd\\Deposito.js", "file": "cypress\\e2e\\FrontEnd\\Deposito.js", "beforeHooks": [], "afterHooks": [], "tests": [], "suites": [{"uuid": "b5760c17-84d0-462b-ab14-699549d5141f", "title": "Deve validar a geração de pix", "fullFile": "", "file": "", "beforeHooks": [], "afterHooks": [], "tests": [{"title": "Gerar pix com sucesso", "fullTitle": "Deve validar a geração de pix Gerar pix com sucesso", "timedOut": null, "duration": 35105, "state": "passed", "speed": "slow", "pass": true, "fail": false, "pending": false, "context": null, "code": "cy.get('.nebulosa-header__buttomAndBadgeWrapper').contains('button', 'Depositar');\ncy.clickByContains('.nebulosa-header__buttomAndBadgeWrapper', 'Depositar');\n//Validando modal de valores\n//Título\ncy.contains('h1', 'Depósito rápido').should('be.visible');\n//Título campo e valor pré selecionado\ncy.validateText('.nebulosa-input__Root > label', 'Valor do depósito');\ncy.get('.nebulosa-input__Input > input:eq(1)').should('have.value', 'R$ 50,00');\n//Validando opções disponíveis para depósito\ncy.validateText('.d_flex > div > h3', 'Valor mínimo R$ 1,00 e valor máximo R$ 45.000,00');\ncy.validateText('[type=\"button\"] > span:eq(0)', 'R$ 10');\ncy.validateText('[type=\"button\"] > span:eq(1)', 'R$ 50');\ncy.validateText('[type=\"button\"] > span:eq(2)', 'R$ 100');\ncy.validateText('[type=\"button\"] > span:eq(3)', 'R$ 250');\ncy.validateText('[type=\"button\"] > span:eq(4)', 'R$ 500');\ncy.validateText('[type=\"button\"] > span:eq(5)', 'R$ 1.000');\n//validando botão e mensagem de maior de 18\ncy.validateText('[type=\"submit\"]', 'Depositar');\n//cy.validateText('form > div > .d_flex > .ai_center > p', 'Depósitos proibidos para menores de 18 anos.')\n//Botão x\ncy.isVisible('[data-testid=\"closeDepositModalButton\"]');\n//Banner principal\ncy.isVisible('img[src*=\"985f43e8-ccfa-4e8f-9770-1144f09b229f\"]');\n//clicando para avançar\ncy.clickMouse('[type=\"submit\"]', 'Depositar');\ncy.wait('@gerandoQrCode', {\n  timeout: 15000\n}).its('response.statusCode').should('eq', 200);\n//Label depósito/pix\ncy.clickByContains('h1', 'Código Pix disponível');\ncy.clickByContains('p', 'Copie o código e utilize o PIX Copia e Cola no aplicativo do seu banco.');\ncy.isVisible('button', 'Saiba como fazer o pagamento com Pix');\ncy.clickByContains('button', 'Saiba como fazer o pagamento com Pix');\n//Listagem com a informaços do Saiba como fazer o pagamento com Pix\ncy.get('ul > ul').eq(0).within(() => {\n  cy.contains('p', '1. Acesse o aplicativo do seu banco e escolha a opção Pix Copia e Cola” e cole o código').should('be.visible');\n});\ncy.get('ul > ul').eq(1).within(() => {\n  cy.contains('p', '2. Confirme as informações e finalize o pagamento').should('be.visible');\n});\ncy.get('ul > ul').eq(2).within(() => {\n  cy.contains('p', '3. Seu pagamento será aprovado em alguns segundos!').should('be.visible');\n});\ncy.isVisible('p', 'Pagamentos feitos por outro CPF ou de contas empresariais serão automaticamente rejeitados');\ncy.isVisible('button', 'Exibir QR Code');\ncy.clickByContains('button', 'Exibir QR Code');\ncy.get('img').should('exist');\n// Captura o valor inicial do contador\ncy.get('.pt_xxxs > .d_flex > .ai_center > div.fw_regular').invoke('text').then(initialValue => {\n  // Espera 1 segundo (ou o tempo que o contador deve mudar)\n  cy.wait(1000);\n  // Captura novamente e compara\n  cy.get('.pt_xxxs > .d_flex > .ai_center > div.fw_regular').invoke('text').should(newValue => {\n    expect(newValue).to.not.eq(initialValue);\n  });\n});\ncy.validateText('.gap_md > .mb_xxxs > .fs_xxs', 'Valor do depósito:');\ncy.wait(2000);\ncy.contains('.mb_xxxs > .fs_xs', 'R$ 50,00').should('be.visible');\n//botao Copiar código Pix\ncy.contains('button', 'Copiar código Pix').should('have.attr', 'lefticon', 'brand-pix');\ncy.stubClipboard();\ncy.get('[lefticon=\"brand-pix\"] > span').click();\ncy.contains('Código PIX copiado!').should('be.visible');", "err": {}, "uuid": "50954a55-3c35-422d-8cb6-23f49eee38a5", "parentUUID": "b5760c17-84d0-462b-ab14-699549d5141f", "isHook": false, "skipped": false}], "suites": [], "passes": ["50954a55-3c35-422d-8cb6-23f49eee38a5"], "failures": [], "pending": [], "skipped": [], "duration": 35105, "root": false, "rootEmpty": false, "_timeout": 2000}], "passes": [], "failures": [], "pending": [], "skipped": [], "duration": 0, "root": true, "rootEmpty": true, "_timeout": 2000}], "meta": {"mocha": {"version": "7.2.0"}, "mochawesome": {"options": {"quiet": false, "reportFilename": "[name]-report-[datetime]", "saveHtml": true, "saveJson": true, "consoleReporter": "spec", "useInlineDiffs": false, "code": true}, "version": "7.1.3"}, "marge": {"options": {"reportDir": "cypress/results", "overwrite": true, "html": true, "json": true, "reportFilename": "[name]-report-[datetime]"}, "version": "6.2.0"}}}
{"uuid": "fb8301e7-0341-46ba-9ef6-4340309b6359", "historyId": "6ad7caa030fa655e26b0e84584781625", "status": "passed", "statusDetails": null, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [{"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [], "start": 1755518970720, "name": "https://bff-estrelabet.hml.estrelabet.bet.br/validate/telephoneNumber POST", "stop": 1755518970720}], "attachments": [], "parameters": [], "start": 1755518970580, "name": "api (\"{ method: 'POST', url: 'https://bff-estrelabet.hml.estrelabet.be'... 29 more characters, body: { isdCode: '55', telephoneNumber: '41121321231' } }\")", "stop": 1755518970720}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": true}, {"name": "expected", "value": true}], "start": 1755518970724, "name": "assert expected **true** to be true", "stop": 1755518970724}, {"status": "passed", "statusDetails": {}, "stage": "finished", "steps": [], "attachments": [], "parameters": [{"name": "actual", "value": 200}, {"name": "expected", "value": 200}], "start": 1755518970724, "name": "assert expected **200** to equal **200**", "stop": 1755518970724}], "attachments": [], "parameters": [], "labels": [{"name": "package", "value": "cypress.e2e.FrontEnd.Cadastro.js"}, {"name": "suite", "value": "Cadastro"}], "links": [], "start": 1755518970544, "name": "Deve validar número de telefone com sucesso", "fullName": "Deve validar número de telefone com sucesso", "stop": 1755518970726}